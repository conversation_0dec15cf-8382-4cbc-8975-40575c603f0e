import { Layout, LayoutComponent } from "@/types";
import { ReactNode } from "react";
import BaseLayout from "./BaseLayout";
import { fetchLayout } from "@/lib/api/layout.service";
import SectionRenderer from "../renderer/SectionRenderer";
import { fetchSection } from "@/lib/api/section.service";
import ComponentRenderer from "../renderer/ComponentRenderer";
import { fetchComponentById } from "@/lib/api/component.service";
import { BreakpointName } from "@/lib/utils/breakpointUtils";

interface LayoutRendererProps {
  layoutId: string;
  children: ReactNode;
  layoutData?: Layout | null;
  breakpoint?: BreakpointName;
}

export default async function LayoutRenderer({
  layoutId,
  children,
  layoutData,
  breakpoint = "default",
}: LayoutRendererProps) {
  const layout = layoutData || (await fetchLayout(layoutId));
  if (!layout) throw new Error(`Layout "${layoutId}" not found`);

  // Normalize data
  const normalizedLayout = {
    ...layout,
    components: layout.components || [],
  };

  // Determine areas
  const allAreas = new Set<string>();
  normalizedLayout.config.default.areas.forEach((row) => {
    row.split(" ").forEach((area) => area && allAreas.add(area));
  });

  // Map components to areas
  const areaComponents: Record<string, LayoutComponent[]> = {};
  normalizedLayout.components.forEach((component) => {
    if (component.area) {
      areaComponents[component.area] ||= [];
      areaComponents[component.area].push(component);
    }
  });

  // Process areas in parallel
  const areaContent: Record<string, ReactNode> = {};
  await Promise.all(
    Array.from(allAreas).map(async (area) => {
      if (areaComponents[area]?.length) {
        const elements = await Promise.all(
          areaComponents[area].map(async (comp) => {
            try {
              if (comp.componentId) {
                const component = await fetchComponentById(comp.componentId);
                return component ? (
                  <ComponentRenderer key={comp.componentId} data={component} />
                ) : null;
              }
            } catch (error) {
              console.error(`Error rendering component in ${area}:`, error);
              return null;
            }
          })
        );

        areaContent[area] = (
          <div className="h-full">{elements.filter(Boolean)}</div>
        );
      } else {
        // Handle special areas
        switch (area) {
          case "header":
            const nav = await fetchSection("navigation");
            areaContent.header = nav ? <SectionRenderer data={nav} /> : null;
            break;
          case "main":
            areaContent.main = (
              <div className="h-full overflow-auto">{children}</div>
            );
            break;
          case "footer":
            const footer = await fetchSection("footer");
            areaContent.footer = footer ? (
              <SectionRenderer data={footer} />
            ) : null;
            break;
          default:
            areaContent[area] = <div>Area: {area}</div>;
        }
      }
    })
  );

  return (
    <BaseLayout
      layout={normalizedLayout}
      children={areaContent}
      breakpoint={breakpoint}
    />
  );
}
