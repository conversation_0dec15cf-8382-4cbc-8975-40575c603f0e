import { Layout } from "@/types";
import { ReactNode } from "react";

interface BaseLayoutProps {
  layout: Layout;
  children: {
    [key: string]: ReactNode;
  };
  breakpoint?: string;
}

export default function BaseLayout({
  layout,
  children,
  breakpoint = "default",
}: BaseLayoutProps) {
  // Get the configuration for the current breakpoint, or fall back to default
  const config = layout.config[breakpoint] || layout.config.default;

  // Convert grid template areas to CSS grid-template-areas format
  const gridTemplateAreas = config.areas.map((area) => `"${area}"`).join(" ");

  // Create the grid styles
  const gridStyles = {
    display: "grid",
    gridTemplateAreas,
    gridTemplateRows: config.rows.join(" "),
    gridTemplateColumns: config.columns.join(" "),
    gap: config.gap,
    height: "100vh",
    maxHeight: "100vh",
    overflow: "hidden",
    ...layout.styles,
  };

  return (
    <div style={gridStyles} className="layout-container">
      {Object.entries(children).map(([area, content]) => {
        if (!content) return null;

        // Use semantic HTML tags for specific areas
        switch (area) {
          case "header":
            return (
              <header
                key={area}
                style={{ gridArea: area }}
                className="layout-header"
              >
                {content}
              </header>
            );
          case "main":
            return (
              <main
                key={area}
                style={{ gridArea: area, overflow: "auto" }}
                className="layout-main"
              >
                {content}
              </main>
            );
          case "footer":
            return (
              <footer
                key={area}
                style={{ gridArea: area }}
                className="layout-footer"
              >
                {content}
              </footer>
            );
          default:
            return (
              <div
                key={area}
                style={{ gridArea: area, overflow: "auto" }}
                className={`layout-area layout-${area}`}
              >
                {content}
              </div>
            );
        }
      })}
    </div>
  );
}
