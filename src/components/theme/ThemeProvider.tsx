import { fetchThemeSettings } from "@/lib/api/theme.service";

export default async function ThemeProvider() {
  const theme = await fetchThemeSettings();

  return (
    <style>{`
      :root {
        /* Light mode colors */
        --background: ${theme.light.background};
        --foreground: ${theme.light.foreground};
        --card: ${theme.light.card};
        --card-foreground: ${theme.light.cardForeground};
        --popover: ${theme.light.popover};
        --popover-foreground: ${theme.light.popoverForeground};
        --primary: ${theme.light.primary};
        --primary-foreground: ${theme.light.primaryForeground};
        --secondary: ${theme.light.secondary};
        --secondary-foreground: ${theme.light.secondaryForeground};
        --muted: ${theme.light.muted};
        --muted-foreground: ${theme.light.mutedForeground};
        --accent: ${theme.light.accent};
        --accent-foreground: ${theme.light.accentForeground};
        --destructive: ${theme.light.destructive};
        --destructive-foreground: ${theme.light.destructiveForeground};
        --border: ${theme.light.border};
        --input: ${theme.light.input};
        --ring: ${theme.light.ring};
        --chart-1: ${theme.light.chart1};
        --chart-2: ${theme.light.chart2};
        --chart-3: ${theme.light.chart3};
        --chart-4: ${theme.light.chart4};
        --chart-5: ${theme.light.chart5};
        --sidebar: ${theme.light.sidebar};
        --sidebar-foreground: ${theme.light.sidebarForeground};
        --sidebar-primary: ${theme.light.sidebarPrimary};
        --sidebar-primary-foreground: ${theme.light.sidebarPrimaryForeground};
        --sidebar-accent: ${theme.light.sidebarAccent};
        --sidebar-accent-foreground: ${theme.light.sidebarAccentForeground};
        --sidebar-border: ${theme.light.sidebarBorder};
        --sidebar-ring: ${theme.light.sidebarRing};

        /* Fonts */
        --font-sans: ${theme.fonts.sans};
        --font-serif: ${theme.fonts.serif};
        --font-mono: ${theme.fonts.mono};

        /* Layout */
        --radius: ${theme.radius};
        --tracking-normal: ${theme.trackingNormal};

        /* Shadows */
        --shadow-2xs: ${theme.shadows["2xs"]};
        --shadow-xs: ${theme.shadows.xs};
        --shadow-sm: ${theme.shadows.sm};
        --shadow: ${theme.shadows.default};
        --shadow-md: ${theme.shadows.md};
        --shadow-lg: ${theme.shadows.lg};
        --shadow-xl: ${theme.shadows.xl};
        --shadow-2xl: ${theme.shadows["2xl"]};
      }

      .dark {
        /* Dark mode colors */
        --background: ${theme.dark.background};
        --foreground: ${theme.dark.foreground};
        --card: ${theme.dark.card};
        --card-foreground: ${theme.dark.cardForeground};
        --popover: ${theme.dark.popover};
        --popover-foreground: ${theme.dark.popoverForeground};
        --primary: ${theme.dark.primary};
        --primary-foreground: ${theme.dark.primaryForeground};
        --secondary: ${theme.dark.secondary};
        --secondary-foreground: ${theme.dark.secondaryForeground};
        --muted: ${theme.dark.muted};
        --muted-foreground: ${theme.dark.mutedForeground};
        --accent: ${theme.dark.accent};
        --accent-foreground: ${theme.dark.accentForeground};
        --destructive: ${theme.dark.destructive};
        --destructive-foreground: ${theme.dark.destructiveForeground};
        --border: ${theme.dark.border};
        --input: ${theme.dark.input};
        --ring: ${theme.dark.ring};
        --chart-1: ${theme.dark.chart1};
        --chart-2: ${theme.dark.chart2};
        --chart-3: ${theme.dark.chart3};
        --chart-4: ${theme.dark.chart4};
        --chart-5: ${theme.dark.chart5};
        --sidebar: ${theme.dark.sidebar};
        --sidebar-foreground: ${theme.dark.sidebarForeground};
        --sidebar-primary: ${theme.dark.sidebarPrimary};
        --sidebar-primary-foreground: ${theme.dark.sidebarPrimaryForeground};
        --sidebar-accent: ${theme.dark.sidebarAccent};
        --sidebar-accent-foreground: ${theme.dark.sidebarAccentForeground};
        --sidebar-border: ${theme.dark.sidebarBorder};
        --sidebar-ring: ${theme.dark.sidebarRing};

        /* Fonts remain the same in dark mode */
        --font-sans: ${theme.fonts.sans};
        --font-serif: ${theme.fonts.serif};
        --font-mono: ${theme.fonts.mono};

        /* Layout remains the same in dark mode */
        --radius: ${theme.radius};

        /* Shadows remain the same in dark mode */
        --shadow-2xs: ${theme.shadows["2xs"]};
        --shadow-xs: ${theme.shadows.xs};
        --shadow-sm: ${theme.shadows.sm};
        --shadow: ${theme.shadows.default};
        --shadow-md: ${theme.shadows.md};
        --shadow-lg: ${theme.shadows.lg};
        --shadow-xl: ${theme.shadows.xl};
        --shadow-2xl: ${theme.shadows["2xl"]};
      }

      body {
        font-family: var(--font-sans) !important;
        letter-spacing: var(--tracking-normal);
      }

      * {
        font-family: inherit;
      }
    `}</style>
  );
}
