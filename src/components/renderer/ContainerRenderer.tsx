import { ReactNode } from "react";
import Component<PERSON><PERSON><PERSON> from "./ComponentRenderer";
import { cn } from "@/lib/utils";

interface ContainerElement {
  componentId?: string;
  type?: string;
  area?: string;
  layout?: {
    type: string;
    config: {
      direction?: string;
      columns?: string;
      gap?: string;
      alignItems?: string;
      justifyContent?: string;
      [breakpoint: string]: any;
    };
  };
  elements?: ContainerElement[];
  config?: Record<string, any>;
  styles?: Record<string, string>;
}

interface ContainerProps {
  element: ContainerElement;
  components?: any[];
}

export default async function ContainerRenderer({
  element,
  components = [],
}: ContainerProps) {
  // If this is a component reference, fetch and render the component
  if (element.componentId) {
    const component =
      components.find((c) => c.id === element.componentId) ||
      (await fetchComponentById(element.componentId));

    if (component) {
      return <ComponentRenderer data={component} />;
    }

    return (
      <div className="p-4 border border-dashed border-red-300 rounded">
        <p className="text-sm text-red-600">
          Component not found: <code>{element.componentId}</code>
        </p>
      </div>
    );
  }

  // If this is a direct component definition, render it
  if (element.type && element.config) {
    const componentData = {
      id: `inline-${Math.random().toString(36).substr(2, 9)}`,
      type: element.type,
      config: element.config,
      styles: element.styles,
      layout: element.layout,
      elements: element.elements,
    };

    return <ComponentRenderer data={componentData} />;
  }

  // If this is a container with layout and nested elements
  if (element.layout && element.elements) {
    const containerStyle: React.CSSProperties = { ...element.styles };
    let containerClassName = "container-wrapper";

    // Handle layout configuration
    const layoutType = element.layout.type;
    const layoutConfig = element.layout.config;

    if (layoutType === "flex") {
      containerStyle.display = "flex";
      containerStyle.flexDirection =
        layoutConfig.direction === "column" ? "column" : "row";
      containerStyle.gap = layoutConfig.gap || "1rem";
      containerStyle.alignItems = layoutConfig.alignItems || "stretch";
      containerStyle.justifyContent =
        layoutConfig.justifyContent || "flex-start";

      containerClassName = cn(
        containerClassName,
        "flex",
        layoutConfig.direction === "column" ? "flex-col" : "flex-row",
        layoutConfig.alignItems === "center" && "items-center",
        layoutConfig.justifyContent === "center" && "justify-center"
      );

      // Handle responsive overrides
      if (layoutConfig.mobile) {
        // Add responsive classes for mobile
        containerClassName = cn(
          containerClassName,
          layoutConfig.mobile.direction === "column" && "sm:flex-col",
          layoutConfig.mobile.direction === "row" && "sm:flex-row"
        );
      }
    } else if (layoutType === "grid") {
      containerStyle.display = "grid";
      containerStyle.gridTemplateColumns = layoutConfig.columns || "1fr";
      containerStyle.gap = layoutConfig.gap || "1rem";

      containerClassName = cn(containerClassName, "grid");

      if (layoutConfig.areas) {
        containerStyle.gridTemplateAreas = layoutConfig.areas
          .map((area: string) => `"${area}"`)
          .join(" ");
      }

      if (layoutConfig.rows) {
        containerStyle.gridTemplateRows = Array.isArray(layoutConfig.rows)
          ? layoutConfig.rows.join(" ")
          : layoutConfig.rows;
      }
    }

    return (
      <div
        style={containerStyle}
        className={containerClassName}
        data-container-type={layoutType}
      >
        {element.elements.map((childElement, index) => (
          <ContainerRenderer
            key={`${childElement.componentId || childElement.type || "element"}-${index}`}
            element={childElement}
            components={components}
          />
        ))}
      </div>
    );
  }

  // Fallback for unknown element types
  return (
    <div className="p-4 border border-dashed border-yellow-300 rounded">
      <p className="text-sm text-yellow-600">Unknown element structure</p>
      <pre className="text-xs mt-2 text-yellow-600">
        {JSON.stringify(element, null, 2)}
      </pre>
    </div>
  );
}
function fetchComponentById(componentId: string): any {
  throw new Error("Function not implemented.");
}
