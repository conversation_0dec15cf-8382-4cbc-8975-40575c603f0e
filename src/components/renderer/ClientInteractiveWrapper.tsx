"use client";

import { useState, useRef, useEffect, ReactNode } from "react";

interface Message {
  sender: "user" | "support";
  content: string;
  timestamp: string;
}

interface AutomatedResponse {
  keywords: string[];
  response: string;
}

interface ChatConfig {
  initialMessages: Message[];
  automatedResponses: AutomatedResponse[];
  defaultResponse: string;
}

interface InteractiveConfig {
  initialState?: "collapsed" | "expanded";
  chatConfig?: ChatConfig;
  backdropOnExpand?: boolean;
  animationDuration?: number;
}

interface ClientInteractiveWrapperProps {
  config: InteractiveConfig;
  children: (context: {
    isExpanded: boolean;
    toggleExpanded: () => void;
    messages: Message[];
    inputValue: string;
    setInputValue: (value: string) => void;
    handleSendMessage: () => void;
    newMessageCount: number;
    formatTime: (timestamp: string) => string;
  }) => ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

export default function ClientInteractiveWrapper({
  config,
  children,
  className,
  style,
}: ClientInteractiveWrapperProps) {
  const [isExpanded, setIsExpanded] = useState(
    config.initialState === "expanded"
  );
  const [messages, setMessages] = useState<Message[]>(
    config.chatConfig?.initialMessages || []
  );
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (isExpanded) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isExpanded]);

  const handleSendMessage = () => {
    if (!inputValue.trim() || !config.chatConfig) return;

    // Add user message
    const userMessage: Message = {
      sender: "user",
      content: inputValue,
      timestamp: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");

    // Simulate response after a short delay
    setTimeout(() => {
      const responseMessage: Message = {
        sender: "support",
        content: getAutomatedResponse(inputValue),
        timestamp: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, responseMessage]);
    }, 1000);
  };

  const getAutomatedResponse = (userInput: string): string => {
    if (!config.chatConfig) return "";
    
    const input = userInput.toLowerCase();

    // Check for keyword matches in automated responses
    for (const responseObj of config.chatConfig.automatedResponses) {
      if (
        responseObj.keywords.some((keyword) =>
          input.includes(keyword.toLowerCase())
        )
      ) {
        return responseObj.response;
      }
    }

    // Return default response if no keywords match
    return config.chatConfig.defaultResponse;
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const context = {
    isExpanded,
    toggleExpanded,
    messages,
    inputValue,
    setInputValue,
    handleSendMessage,
    newMessageCount: messages.length - (config.chatConfig?.initialMessages.length || 0),
    formatTime,
  };

  return (
    <>
      <div className={className} style={style}>
        {children(context)}
      </div>

      {/* Backdrop when expanded */}
      {config.backdropOnExpand && isExpanded && (
        <div
          className="fixed inset-0 bg-black/20 z-40 transition-opacity"
          style={{
            transitionDuration: `${config.animationDuration || 300}ms`,
          }}
          onClick={() => setIsExpanded(false)}
        />
      )}
    </>
  );
}
