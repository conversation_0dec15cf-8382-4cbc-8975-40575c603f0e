import { ReactNode } from "react";
import ComponentRenderer from "./ComponentRenderer";

interface SectionProps {
  data: {
    id: string;
    type: string;
    config: {
      layout?: string; // "grid" | "flex" | etc.
      className?: string;
      gridConfig?: {
        areas?: string[];
        rows?: string[];
        columns?: string[];
        gap?: string;
      };
    };
    styles?: Record<string, string>;
    components?: Array<{
      id: string;
      type: string;
      config: any;
      styles?: Record<string, string>;
      area?: string; // For grid layout
    }>;
  };
  children?: ReactNode;
}

export default function SectionRenderer({ data, children }: SectionProps) {
  // Apply custom styles if provided
  const sectionStyle = { ...data.styles };

  // Process layout configuration
  const layoutType = data.config?.layout || "flex";
  let layoutClassName = data.config?.className || "";

  if (layoutType === "grid" && data.config?.gridConfig) {
    const { areas, rows, columns, gap } = data.config.gridConfig;

    // Convert grid template areas to CSS grid-template-areas format
    if (areas) {
      sectionStyle.gridTemplateAreas = areas
        .map((area) => `"${area}"`)
        .join(" ");
    }

    if (rows) {
      sectionStyle.gridTemplateRows = rows.join(" ");
    }

    if (columns) {
      sectionStyle.gridTemplateColumns = columns.join(" ");
    }

    if (gap) {
      sectionStyle.gap = gap;
    }

    sectionStyle.display = "grid";
  } else if (layoutType === "flex") {
    // No mapping here - user provides the correct Tailwind classes directly
    // in the className property
  }

  // For grid layout, organize components by area
  if (layoutType === "grid" && data.components) {
    const areaComponents: Record<string, ReactNode> = {};

    // Group components by their area
    data.components.forEach((component) => {
      if (component.area) {
        areaComponents[component.area] = areaComponents[component.area] || [];
        (areaComponents[component.area] as any[]).push(
          <ComponentRenderer key={component.id} data={component} />
        );
      }
    });

    // Add children to content area if specified
    if (children) {
      areaComponents["content"] = children;
    }

    // Render components in their respective grid areas
    return (
      <section
        style={sectionStyle}
        className={`section-wrapper ${layoutClassName}`}
        data-section-id={data.id}
        data-section-type={data.type}
      >
        {Object.entries(areaComponents).map(([area, content]) => (
          <div key={area} style={{ gridArea: area }}>
            {content}
          </div>
        ))}
      </section>
    );
  }

  // For flex and other layouts, render components sequentially
  return (
    <section
      style={sectionStyle}
      className={`section-wrapper ${layoutClassName}`}
      data-section-id={data.id}
      data-section-type={data.type}
    >
      {data.components?.map((component) => (
        <ComponentRenderer key={component.id} data={component} />
      ))}
      {children}
    </section>
  );
}
