import { Page } from "@/types";
import { fetchComponentsByIds } from "@/lib/api/component.service";
import ComponentRenderer from "./ComponentRenderer";
import ContainerRenderer from "./ContainerRenderer";

interface PageRendererProps {
  page: Page;
}

// Helper function to extract component IDs from page elements
function extractComponentIds(elements: any[]): string[] {
  const componentIds: string[] = [];

  for (const element of elements) {
    if (element.componentId) {
      componentIds.push(element.componentId);
    }
    // Recursively check nested elements
    if (element.elements && Array.isArray(element.elements)) {
      componentIds.push(...extractComponentIds(element.elements));
    }
  }

  return [...new Set(componentIds)]; // Remove duplicates
}

// Helper function to render page elements
function renderPageElement(element: any, components: any[], index: number) {
  // Skip siteLogo and mainNavigation since they're already rendered
  if (
    element.componentId === "siteLogo" ||
    element.componentId === "mainNavigation"
  ) {
    return null;
  }

  // If this is a component reference, render the component
  if (element.componentId) {
    const component = components.find((c) => c.id === element.componentId);
    if (component) {
      return (
        <div key={`component-${element.componentId}-${index}`} className="mb-4">
          <ComponentRenderer data={component} />
        </div>
      );
    }
    return (
      <div
        key={`missing-${element.componentId}-${index}`}
        className="p-4 border border-dashed border-red-300 rounded mb-4"
      >
        <p className="text-red-600">
          Component not found: {element.componentId}
        </p>
      </div>
    );
  }

  // If this is a container with nested elements, render using ContainerRenderer
  if (element.type === "container" && element.elements) {
    return (
      <div key={`container-${index}`} className="mb-4">
        <ContainerRenderer element={element} components={components} />
      </div>
    );
  }

  // If this is a direct component definition (inline component)
  if (element.type && element.config) {
    const componentData = {
      id: `inline-${element.type}-${index}`,
      type: element.type,
      config: element.config,
      styles: element.styles,
    };

    return (
      <div key={`inline-${element.type}-${index}`} className="mb-4">
        <ComponentRenderer data={componentData} />
      </div>
    );
  }

  // Fallback for unknown element types
  return (
    <div
      key={`unknown-${index}`}
      className="p-4 border border-dashed border-yellow-300 rounded mb-4"
    >
      <p className="text-yellow-600">Unknown element type</p>
      <pre className="text-xs mt-2">{JSON.stringify(element, null, 2)}</pre>
    </div>
  );
}

export default async function PageRenderer({ page }: PageRendererProps) {
  // Extract all component IDs from the page elements
  const componentIds = extractComponentIds(page.elements);

  // Fetch all components needed for this page
  const components = await fetchComponentsByIds(componentIds);

  return (
    <div className="h-full">
      {/* Page Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="space-y-8">
          {page.elements.map((element, index) =>
            renderPageElement(element, components, index)
          )}
        </div>
      </div>
    </div>
  );
}
