"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import ComponentRenderer from "./ComponentRenderer";

interface FormFieldConfig {
  id: string;
  label: string;
  type: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
  options?: Array<{ value: string; label: string }>;
}

interface FormConfig {
  id: string;
  title?: string;
  submitEndpoint: string;
  method: "GET" | "POST";
  className?: string;
  elements: Array<{
    type: string;
    config: any;
  }>;
  successMessage?: string;
}

interface FormRendererProps {
  config: FormConfig;
}

export default function FormRenderer({ config }: FormRendererProps) {
  const [formState, setFormState] = useState<Record<string, any>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(config.submitEndpoint, {
        method: config.method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formState),
      });

      if (!response.ok) {
        throw new Error("Form submission failed");
      }

      setIsSuccess(true);
      setFormState({});
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // If form was successfully submitted, show success message
  if (isSuccess && config.successMessage) {
    return (
      <div
        className={cn(
          "p-6 bg-green-50 text-green-800 rounded-lg",
          config.className
        )}
      >
        <p className="text-center font-medium">{config.successMessage}</p>
        <Button
          onClick={() => setIsSuccess(false)}
          variant="outline"
          className="mt-4 mx-auto block"
        >
          Submit Another Request
        </Button>
      </div>
    );
  }

  // Render form fields based on their type
  const renderFormField = (field: {
    type: string;
    config: FormFieldConfig;
  }) => {
    const { id, label, type, placeholder, required, className, options } =
      field.config;

    switch (type) {
      case "text":
      case "email":
      case "tel":
      case "date":
      case "number":
        return (
          <div key={id} className={className}>
            <Label htmlFor={id}>
              {label}
              {required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Input
              id={id}
              name={id}
              type={type}
              placeholder={placeholder}
              required={required}
              value={formState[id] || ""}
              onChange={handleInputChange}
              className="mt-1"
            />
          </div>
        );

      case "textarea":
        return (
          <div key={id} className={className}>
            <Label htmlFor={id}>
              {label}
              {required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <Textarea
              id={id}
              name={id}
              placeholder={placeholder}
              required={required}
              value={formState[id] || ""}
              onChange={handleInputChange}
              className="mt-1"
            />
          </div>
        );

      case "select":
        return (
          <div key={id} className={className}>
            <Label htmlFor={id}>
              {label}
              {required && <span className="text-destructive ml-1">*</span>}
            </Label>
            <select
              id={id}
              name={id}
              required={required}
              value={formState[id] || ""}
              onChange={handleInputChange}
              className="w-full p-2 border rounded-md mt-1"
            >
              <option value="">Select an option</option>
              {options?.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <form onSubmit={handleSubmit} className={config.className}>
      {config.elements.map((element, index) => {
        if (element.type === "formField") {
          return renderFormField(element);
        }

        // Handle submit button specially
        if (element.type === "button" && element.config.type === "submit") {
          return (
            <Button
              key={`form-button-${index}`}
              type="submit"
              variant={element.config.variant || "default"}
              size={element.config.size || "default"}
              className={element.config.className}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Submitting..." : element.config.label}
            </Button>
          );
        }

        // For non-form field elements (like headers, paragraphs, etc.)
        return (
          <ComponentRenderer
            key={`form-element-${index}`}
            data={{
              id: `form-element-${index}`,
              type: element.type,
              config: element.config,
            }}
          />
        );
      })}

      {error && (
        <div className="p-3 bg-destructive/10 text-destructive rounded-md mb-4">
          <p>{error}</p>
        </div>
      )}
    </form>
  );
}
