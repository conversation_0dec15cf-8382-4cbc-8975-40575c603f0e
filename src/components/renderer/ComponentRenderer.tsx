import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { NavigationRenderer } from "../navigation/NavigationRenderer";
import { MobileNavigationRenderer } from "../navigation/MobileNavigationRenderer";
import { Component } from "@/types";
import { cn } from "@/lib/utils";
import { ImageComponent } from "../primitives/ImageComponent";
import { CardComponent } from "../primitives/CardComponent";
import { HeaderComponent } from "../primitives/HeaderComponent";
import { ParagraphComponent } from "../primitives/ParahraphComponent";
import IconComponent from "../primitives/IconComponent";
import FormRenderer from "./FormRenderer";
import { LinkComponent } from "../primitives/LinkComponent";
import { IFrameComponent } from "../primitives/IFrameComponent";
import MobileChatRenderer from "./MobileChatRenderer";
import ChatRenderer from "./ChatRenderer";

interface ComponentProps {
  data: Component;
}

export default function ComponentRenderer({ data }: ComponentProps) {
  // Apply custom styles if provided
  const componentStyle = data.styles || {};

  // Render the appropriate component based on type
  const renderComponent = () => {
    switch (data.type) {
      case "container": {
        const layoutType = data.layout?.type || "flex";
        const layoutConfig = data.layout?.config || {};
        const containerStyle: React.CSSProperties = { ...componentStyle };
        let containerClassName = data.config?.className || "";

        if (layoutType === "flex") {
          containerStyle.display = "flex";
          containerStyle.flexDirection = layoutConfig.direction || "row";
          containerStyle.gap = layoutConfig.gap || "1rem";
          containerStyle.alignItems = layoutConfig.alignItems || "stretch";
          containerStyle.justifyContent =
            layoutConfig.justifyContent || "flex-start";

          containerClassName = cn(
            containerClassName,
            "flex",
            layoutConfig.direction === "column" ? "flex-col" : "flex-row",
            layoutConfig.alignItems === "center" && "items-center",
            layoutConfig.justifyContent === "center" && "justify-center"
          );
        } else if (layoutType === "grid") {
          containerStyle.display = "grid";
          containerStyle.gridTemplateColumns = layoutConfig.columns || "1fr";
          containerStyle.gap = layoutConfig.gap || "1rem";

          containerClassName = cn(containerClassName, "grid");
        }

        return (
          <div style={containerStyle} className={containerClassName}>
            {data.elements?.map((element, index) => (
              <ComponentRenderer
                key={`${element.type}-${index}`}
                data={element}
              />
            ))}
          </div>
        );
      }

      case "card": {
        return (
          <CardComponent
            title={data.config.title}
            description={data.config.description}
            content={data.config.elements}
            footer={data.config.footer}
            className={data.config.className}
          />
        );
      }

      case "image":
        return (
          <ImageComponent
            src={data.config.src}
            alt={data.config.altText}
            link={data.config.link}
            width={data.config.width}
            height={data.config.height}
            className={data.config.className}
          />
        );

      case "header":
        return (
          <HeaderComponent
            content={data.config.content}
            type={data.config.type}
            className={data.config.className}
          />
        );

      case "paragraph":
        return (
          <ParagraphComponent
            content={data.config.content}
            className={data.config.className}
          />
        );

      case "button":
        return (
          <Button
            variant={data.config.variant || "default"}
            size={data.config.size || "default"}
            className={data.config.className}
            asChild={!!data.config.link}
          >
            {data.config.link ? (
              <Link href={data.config.link}>{data.config.label}</Link>
            ) : (
              data.config.label
            )}
          </Button>
        );

      case "icon": {
        return (
          <IconComponent
            name={data.config.name}
            className={data.config.className}
          />
        );
      }

      case "navigationMenu":
        return <NavigationRenderer items={data.config.items} />;

      case "mobileNavigationMenu":
        return <MobileNavigationRenderer items={data.config.items} />;

      case "logo":
        return (
          <div className={cn("font-bold text-xl", data.config?.className)}>
            {data.config.src ? (
              <Image
                src={data.config.src}
                alt={data.config.altText || "Logo"}
                width={data.config.width || 150}
                height={data.config.height || 40}
              />
            ) : (
              data.config.text || "My App"
            )}
          </div>
        );

      case "actions":
        return (
          <div className={cn("flex gap-3", data.config?.className)}>
            {data.config.items?.map((item: any, index: number) => (
              <Button
                key={item.id || index}
                variant={item.variant || "default"}
                size={item.size || "default"}
                className={item.className}
                asChild={!!item.link}
              >
                {item.link ? (
                  <Link href={item.link}>{item.label}</Link>
                ) : (
                  item.label
                )}
              </Button>
            ))}
          </div>
        );

      case "form":
        return <FormRenderer config={data.config} />;

      case "link":
        return (
          <LinkComponent
            href={data.config.href}
            content={data.config.content}
            className={data.config.className}
            external={data.config.external}
          />
        );

      case "iframe":
        return (
          <IFrameComponent
            src={data.config.src}
            title={data.config.title}
            width={data.config.width}
            height={data.config.height}
            className={data.config.className}
            allowFullScreen={data.config.allowFullScreen}
          />
        );

      case "chat":
        return (
          <ChatRenderer
            config={data.config}
            className={data.config.className}
            style={data.styles}
          />
        );

      case "mobileChat":
        return (
          <MobileChatRenderer
            config={data.config}
            className={data.config.className}
            style={data.styles}
          />
        );

      // Add more primitive components as needed

      default:
        return (
          <div className="p-4 bg-red-200 border border-dashed border-gray-300 rounded">
            <p className="text-sm text-muted-foreground">
              Unknown component type: <code>{data.type}</code>
            </p>
            <pre className="text-xs mt-2 text-muted-foreground">
              {JSON.stringify(data.config, null, 2)}
            </pre>
          </div>
        );
    }
  };

  return (
    //TODO: Render data.id & data.type or namings for future debugging & analytics
    // <div data-component-id={data.id} data-component-type={data.type}>
    <>{renderComponent()}</>
    // </div>
  );
}
