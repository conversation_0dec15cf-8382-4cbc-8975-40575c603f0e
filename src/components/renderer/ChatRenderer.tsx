"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send } from "lucide-react";

interface Message {
  sender: "user" | "support";
  content: string;
  timestamp: string;
}

interface AutomatedResponse {
  keywords: string[];
  response: string;
}

interface ChatConfig {
  title: string;
  subtitle?: string;
  placeholder: string;
  sendButtonText: string;
  initialMessages: Message[];
  automatedResponses: AutomatedResponse[];
  defaultResponse: string;
}

interface ChatRendererProps {
  config: ChatConfig;
  className?: string;
  style?: React.CSSProperties;
}

export default function ChatRenderer({
  config,
  className,
  style,
}: ChatRendererProps) {
  const [messages, setMessages] = useState<Message[]>(
    config.initialMessages || []
  );
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      sender: "user",
      content: inputValue,
      timestamp: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");

    // Simulate response after a short delay
    setTimeout(() => {
      const responseMessage: Message = {
        sender: "support",
        content: getAutomatedResponse(inputValue),
        timestamp: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, responseMessage]);
    }, 1000);
  };

  const getAutomatedResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    // Check for keyword matches in automated responses
    for (const responseObj of config.automatedResponses) {
      if (
        responseObj.keywords.some((keyword) =>
          input.includes(keyword.toLowerCase())
        )
      ) {
        return responseObj.response;
      }
    }

    // Return default response if no keywords match
    return config.defaultResponse;
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <div
      className={`bg-card h-full flex flex-col ${className || ""}`}
      style={style}
    >
      <div className="p-4 border-b border-border">
        <h2 className="font-bold text-lg">{config.title}</h2>
        {config.subtitle && (
          <p className="text-xs text-muted-foreground">{config.subtitle}</p>
        )}
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`${
                message.sender === "support"
                  ? "bg-muted p-3 rounded-lg"
                  : "bg-primary/10 p-3 rounded-lg ml-8"
              }`}
            >
              <div className="flex justify-between items-center mb-1">
                <p className="text-sm font-semibold">
                  {message.sender === "support" ? "Support" : "You"}
                </p>
                <span className="text-xs text-muted-foreground">
                  {formatTime(message.timestamp)}
                </span>
              </div>
              <p className="text-sm">{message.content}</p>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <Input
            type="text"
            placeholder={config.placeholder}
            className="flex-1 p-2 border border-input rounded-md"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <Button
            className="bg-primary text-primary-foreground px-4 py-2 rounded-md"
            onClick={handleSendMessage}
            disabled={!inputValue.trim()}
          >
            {config.sendButtonText || <Send size={18} />}
          </Button>
        </div>
      </div>
    </div>
  );
}
