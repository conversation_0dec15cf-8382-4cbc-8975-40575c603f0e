import { cn } from "@/lib/utils";
import ComponentRenderer from "./ComponentRenderer";
import ClientInteractiveWrapper from "./ClientInteractiveWrapper";

interface Message {
  sender: "user" | "support";
  content: string;
  timestamp: string;
}

interface AutomatedResponse {
  keywords: string[];
  response: string;
}

interface ChatConfig {
  initialMessages: Message[];
  automatedResponses: AutomatedResponse[];
  defaultResponse: string;
}

interface InteractiveContainerConfig {
  className?: string;
  initialState?: "collapsed" | "expanded";
  expandedHeight?: string;
  collapsedHeight?: string;
  backdropOnExpand?: boolean;
  interactiveContainer?: boolean;
  chatConfig?: ChatConfig;
  animationDuration?: number;
  [key: string]: any;
}

interface LayoutConfig {
  type: string;
  config: {
    direction?: string;
    gap?: string;
    justifyContent?: string;
    alignItems?: string;
    [key: string]: any;
  };
}

interface Element {
  id: string;
  type: string;
  config: any;
  layout?: LayoutConfig;
  elements?: Element[];
  styles?: Record<string, string>;
}

interface InteractiveContainerRendererProps {
  config: InteractiveContainerConfig;
  layout?: LayoutConfig;
  elements?: Element[];
  className?: string;
  style?: React.CSSProperties;
}

export default function InteractiveContainerRenderer({
  config,
  layout,
  elements = [],
  className,
  style,
}: InteractiveContainerRendererProps) {
  // Determine container height based on state
  const getContainerHeight = (isExpanded: boolean) => {
    if (config.interactiveContainer) {
      if (isExpanded && config.expandedHeight) {
        return config.expandedHeight;
      }
      if (!isExpanded && config.collapsedHeight) {
        return config.collapsedHeight;
      }
    }
    return undefined;
  };

  // Apply layout styles
  const getLayoutStyle = (isExpanded: boolean): React.CSSProperties => {
    const layoutStyle: React.CSSProperties = { ...style };

    if (layout?.type === "flex") {
      layoutStyle.display = "flex";
      layoutStyle.flexDirection =
        layout.config.direction === "column" ? "column" : "row";
      layoutStyle.gap = layout.config.gap || "1rem";
      layoutStyle.alignItems = layout.config.alignItems || "stretch";
      layoutStyle.justifyContent = layout.config.justifyContent || "flex-start";
    } else if (layout?.type === "grid") {
      layoutStyle.display = "grid";
      layoutStyle.gridTemplateColumns = layout.config.columns || "1fr";
      layoutStyle.gap = layout.config.gap || "1rem";
    }

    // Set height for interactive containers
    if (config.interactiveContainer) {
      const height = getContainerHeight(isExpanded);
      if (height) {
        layoutStyle.height = height;
      }
    }

    return layoutStyle;
  };

  const getLayoutClassName = () => {
    let layoutClassName = cn(config.className, className);

    if (layout?.type === "flex") {
      layoutClassName = cn(
        layoutClassName,
        "flex",
        layout.config.direction === "column" ? "flex-col" : "flex-row",
        layout.config.alignItems === "center" && "items-center",
        layout.config.justifyContent === "center" && "justify-center"
      );
    } else if (layout?.type === "grid") {
      layoutClassName = cn(layoutClassName, "grid");
    }

    return layoutClassName;
  };

  const renderElement = (element: Element, context: any) => {
    // Handle conditional display
    if (element.config.conditionalDisplay) {
      const condition = element.config.conditionalDisplay.condition;
      const showWhen = element.config.conditionalDisplay.showWhen;

      let shouldShow = false;

      if (condition === "expanded") {
        shouldShow = context.isExpanded;
      } else if (condition === "collapsed") {
        shouldShow = !context.isExpanded;
      } else if (condition === "hasNewMessages && collapsed") {
        shouldShow = context.newMessageCount > 0 && !context.isExpanded;
      }

      if (shouldShow !== showWhen) {
        return null;
      }
    }

    // Handle dynamic content
    if (element.config.dynamicContent === "messages") {
      return (
        <div key={element.id} className={element.config.className}>
          {context.messages.map((message: Message, index: number) => (
            <div
              key={index}
              className={cn(
                "p-3 rounded-lg",
                message.sender === "support" ? "bg-muted" : "bg-primary/10 ml-8"
              )}
            >
              <div className="flex justify-between items-center mb-1">
                <p className="text-sm font-semibold">
                  {message.sender === "support" ? "Support" : "You"}
                </p>
                <span className="text-xs text-muted-foreground">
                  {context.formatTime(message.timestamp)}
                </span>
              </div>
              <p className="text-sm">{message.content}</p>
            </div>
          ))}
        </div>
      );
    }

    // Handle click actions
    const elementProps: any = {};
    if (element.config.clickAction === "toggleExpand") {
      elementProps.onClick = context.toggleExpanded;
    }

    // Handle conditional icons
    let iconName = element.config.name;
    if (element.config.conditionalIcon) {
      iconName = context.isExpanded
        ? element.config.conditionalIcon.expanded
        : element.config.conditionalIcon.collapsed;
    }

    // Handle dynamic content replacement
    let content = element.config.content;
    if (content && content.includes("{newMessageCount}")) {
      content = content.replace(
        "{newMessageCount}",
        context.newMessageCount.toString()
      );
    }

    // Create enhanced element config
    const enhancedElement = {
      ...element,
      config: {
        ...element.config,
        name: iconName,
        content: content,
        value:
          element.config.type === "input"
            ? context.inputValue
            : element.config.value,
        onChange:
          element.config.type === "input"
            ? (e: any) => context.setInputValue(e.target.value)
            : undefined,
        onKeyPress:
          element.config.onEnter === "sendMessage"
            ? (e: any) => {
                if (e.key === "Enter") context.handleSendMessage();
              }
            : undefined,
        onClick:
          element.config.action === "sendMessage"
            ? context.handleSendMessage
            : elementProps.onClick,
        disabled:
          element.config.disabled === "inputEmpty"
            ? !context.inputValue.trim()
            : element.config.disabled,
        ...elementProps,
      },
    };

    return <ComponentRenderer key={element.id} data={enhancedElement} />;
  };

  return (
    <ClientInteractiveWrapper
      config={{
        initialState: config.initialState,
        chatConfig: config.chatConfig,
        backdropOnExpand: config.backdropOnExpand,
        animationDuration: config.animationDuration,
      }}
      className={getLayoutClassName()}
      style={getLayoutStyle(false)} // Initial style, will be updated by wrapper
    >
      {(context) => (
        <div
          className={getLayoutClassName()}
          style={getLayoutStyle(context.isExpanded)}
        >
          {elements.map((element) => renderElement(element, context))}
        </div>
      )}
    </ClientInteractiveWrapper>
  );
}
