"use client";

import { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import ComponentRenderer from "./ComponentRenderer";

interface Message {
  sender: "user" | "support";
  content: string;
  timestamp: string;
}

interface AutomatedResponse {
  keywords: string[];
  response: string;
}

interface ChatConfig {
  initialMessages: Message[];
  automatedResponses: AutomatedResponse[];
  defaultResponse: string;
}

interface InteractiveContainerConfig {
  className?: string;
  initialState?: "collapsed" | "expanded";
  expandedHeight?: string;
  collapsedHeight?: string;
  backdropOnExpand?: boolean;
  interactiveContainer?: boolean;
  chatConfig?: ChatConfig;
  [key: string]: any;
}

interface LayoutConfig {
  type: string;
  config: {
    direction?: string;
    gap?: string;
    justifyContent?: string;
    alignItems?: string;
    [key: string]: any;
  };
}

interface Element {
  id: string;
  type: string;
  config: any;
  layout?: LayoutConfig;
  elements?: Element[];
  styles?: Record<string, string>;
}

interface InteractiveContainerRendererProps {
  config: InteractiveContainerConfig;
  layout?: LayoutConfig;
  elements?: Element[];
  className?: string;
  style?: React.CSSProperties;
}

export default function InteractiveContainerRenderer({
  config,
  layout,
  elements = [],
  className,
  style,
}: InteractiveContainerRendererProps) {
  const [isExpanded, setIsExpanded] = useState(
    config.initialState === "expanded"
  );
  const [messages, setMessages] = useState<Message[]>(
    config.chatConfig?.initialMessages || []
  );
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (isExpanded) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isExpanded]);

  const handleSendMessage = () => {
    if (!inputValue.trim() || !config.chatConfig) return;

    // Add user message
    const userMessage: Message = {
      sender: "user",
      content: inputValue,
      timestamp: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");

    // Simulate response after a short delay
    setTimeout(() => {
      const responseMessage: Message = {
        sender: "support",
        content: getAutomatedResponse(inputValue),
        timestamp: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, responseMessage]);
    }, 1000);
  };

  const getAutomatedResponse = (userInput: string): string => {
    if (!config.chatConfig) return "";
    
    const input = userInput.toLowerCase();

    // Check for keyword matches in automated responses
    for (const responseObj of config.chatConfig.automatedResponses) {
      if (
        responseObj.keywords.some((keyword) =>
          input.includes(keyword.toLowerCase())
        )
      ) {
        return responseObj.response;
      }
    }

    // Return default response if no keywords match
    return config.chatConfig.defaultResponse;
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  // Create context for child components
  const componentContext = {
    isExpanded,
    toggleExpanded,
    messages,
    inputValue,
    setInputValue,
    handleSendMessage,
    newMessageCount: messages.length - (config.chatConfig?.initialMessages.length || 0),
    formatTime,
  };

  // Determine container height based on state
  const getContainerHeight = () => {
    if (config.interactiveContainer) {
      if (isExpanded && config.expandedHeight) {
        return config.expandedHeight;
      }
      if (!isExpanded && config.collapsedHeight) {
        return config.collapsedHeight;
      }
    }
    return undefined;
  };

  // Apply layout styles
  const layoutStyle: React.CSSProperties = { ...style };
  let layoutClassName = cn(config.className, className);

  if (layout?.type === "flex") {
    layoutStyle.display = "flex";
    layoutStyle.flexDirection = layout.config.direction === "column" ? "column" : "row";
    layoutStyle.gap = layout.config.gap || "1rem";
    layoutStyle.alignItems = layout.config.alignItems || "stretch";
    layoutStyle.justifyContent = layout.config.justifyContent || "flex-start";

    layoutClassName = cn(
      layoutClassName,
      "flex",
      layout.config.direction === "column" ? "flex-col" : "flex-row",
      layout.config.alignItems === "center" && "items-center",
      layout.config.justifyContent === "center" && "justify-center"
    );
  } else if (layout?.type === "grid") {
    layoutStyle.display = "grid";
    layoutStyle.gridTemplateColumns = layout.config.columns || "1fr";
    layoutStyle.gap = layout.config.gap || "1rem";
    layoutClassName = cn(layoutClassName, "grid");
  }

  // Set height for interactive containers
  if (config.interactiveContainer) {
    const height = getContainerHeight();
    if (height) {
      layoutStyle.height = height;
    }
  }

  const renderElement = (element: Element) => {
    // Handle conditional display
    if (element.config.conditionalDisplay) {
      const condition = element.config.conditionalDisplay.condition;
      const showWhen = element.config.conditionalDisplay.showWhen;
      
      let shouldShow = false;
      
      if (condition === "expanded") {
        shouldShow = isExpanded;
      } else if (condition === "collapsed") {
        shouldShow = !isExpanded;
      } else if (condition === "hasNewMessages && collapsed") {
        shouldShow = componentContext.newMessageCount > 0 && !isExpanded;
      }
      
      if (shouldShow !== showWhen) {
        return null;
      }
    }

    // Handle dynamic content
    if (element.config.dynamicContent === "messages") {
      return (
        <div key={element.id} className={element.config.className}>
          {messages.map((message, index) => (
            <div
              key={index}
              className={cn(
                "p-3 rounded-lg",
                message.sender === "support"
                  ? "bg-muted"
                  : "bg-primary/10 ml-8"
              )}
            >
              <div className="flex justify-between items-center mb-1">
                <p className="text-sm font-semibold">
                  {message.sender === "support" ? "Support" : "You"}
                </p>
                <span className="text-xs text-muted-foreground">
                  {formatTime(message.timestamp)}
                </span>
              </div>
              <p className="text-sm">{message.content}</p>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      );
    }

    // Handle click actions
    const elementProps: any = {};
    if (element.config.clickAction === "toggleExpand") {
      elementProps.onClick = toggleExpanded;
    }

    // Handle conditional icons
    let iconName = element.config.name;
    if (element.config.conditionalIcon) {
      iconName = isExpanded 
        ? element.config.conditionalIcon.expanded 
        : element.config.conditionalIcon.collapsed;
    }

    // Handle dynamic content replacement
    let content = element.config.content;
    if (content && content.includes("{newMessageCount}")) {
      content = content.replace("{newMessageCount}", componentContext.newMessageCount.toString());
    }

    // Create enhanced element config
    const enhancedElement = {
      ...element,
      config: {
        ...element.config,
        name: iconName,
        content: content,
        value: element.config.type === "input" ? inputValue : element.config.value,
        onChange: element.config.type === "input" ? (e: any) => setInputValue(e.target.value) : undefined,
        onKeyPress: element.config.onEnter === "sendMessage" ? (e: any) => {
          if (e.key === "Enter") handleSendMessage();
        } : undefined,
        onClick: element.config.action === "sendMessage" ? handleSendMessage : elementProps.onClick,
        disabled: element.config.disabled === "inputEmpty" ? !inputValue.trim() : element.config.disabled,
        ...elementProps,
      },
    };

    return <ComponentRenderer key={element.id} data={enhancedElement} />;
  };

  return (
    <>
      <div
        className={layoutClassName}
        style={layoutStyle}
      >
        {elements.map(renderElement)}
      </div>

      {/* Backdrop when expanded */}
      {config.backdropOnExpand && isExpanded && (
        <div
          className="fixed inset-0 bg-black/20 z-40 transition-opacity duration-300"
          onClick={() => setIsExpanded(false)}
        />
      )}
    </>
  );
}
