"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, ChevronUp, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

interface Message {
  sender: "user" | "support";
  content: string;
  timestamp: string;
}

interface AutomatedResponse {
  keywords: string[];
  response: string;
}

interface MobileChatConfig {
  title: string;
  subtitle?: string;
  placeholder: string;
  sendButtonText: string;
  initialState: "collapsed" | "expanded";
  expandTrigger: "header" | "button" | "icon";
  animationDuration: number;
  positioning: "bottom" | "top" | "overlay";
  expandedHeight: string;
  collapsedHeight: string;
  showNewMessageBadge: boolean;
  backdropOnExpand: boolean;
  initialMessages: Message[];
  automatedResponses: AutomatedResponse[];
  defaultResponse: string;
}

interface MobileChatRendererProps {
  config: MobileChatConfig;
  className?: string;
  style?: React.CSSProperties;
}

export default function MobileChatRenderer({
  config,
  className,
  style,
}: MobileChatRendererProps) {
  const [messages, setMessages] = useState<Message[]>(
    config.initialMessages || []
  );
  const [inputValue, setInputValue] = useState("");
  const [isExpanded, setIsExpanded] = useState(
    config.initialState === "expanded"
  );
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (isExpanded) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isExpanded]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      sender: "user",
      content: inputValue,
      timestamp: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");

    // Simulate response after a short delay
    setTimeout(() => {
      const responseMessage: Message = {
        sender: "support",
        content: getAutomatedResponse(inputValue),
        timestamp: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, responseMessage]);
    }, 1000);
  };

  const getAutomatedResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    // Check for keyword matches in automated responses
    for (const responseObj of config.automatedResponses) {
      if (
        responseObj.keywords.some((keyword) =>
          input.includes(keyword.toLowerCase())
        )
      ) {
        return responseObj.response;
      }
    }

    // Return default response if no keywords match
    return config.defaultResponse;
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const newMessageCount = messages.length - config.initialMessages.length;

  // Determine positioning classes based on config
  const getPositioningClasses = () => {
    switch (config.positioning) {
      case "top":
        return "top-0 left-0 right-0";
      case "overlay":
        return "inset-0";
      case "bottom":
      default:
        return "bottom-0 left-0 right-0";
    }
  };

  // Determine container height based on state
  const getContainerHeight = () => {
    if (isExpanded) {
      return config.expandedHeight;
    }
    return config.collapsedHeight;
  };

  return (
    <>
      {/* Mobile Chat Container */}
      <div
        className={cn(
          "fixed bg-card border-t border-border transition-all ease-in-out z-50",
          getPositioningClasses(),
          className
        )}
        style={{
          ...style,
          height: getContainerHeight(),
          transitionDuration: `${config.animationDuration}ms`,
        }}
      >
        {/* Header - Always Visible */}
        <div
          className={cn(
            "p-4 border-b border-border flex items-center justify-between",
            config.expandTrigger === "header" && "cursor-pointer"
          )}
          onClick={config.expandTrigger === "header" ? toggleExpanded : undefined}
        >
          <div className="flex-1">
            <h2 className="font-bold text-lg">{config.title}</h2>
            {config.subtitle && !isExpanded && (
              <p className="text-xs text-muted-foreground">{config.subtitle}</p>
            )}
          </div>
          <div className="flex items-center gap-2">
            {config.showNewMessageBadge && newMessageCount > 0 && !isExpanded && (
              <div className="flex items-center gap-1">
                <span className="text-xs text-primary font-medium">
                  {newMessageCount} new
                </span>
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
              </div>
            )}
            {config.expandTrigger === "icon" && (
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleExpanded}
                className="h-8 w-8"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronUp className="h-4 w-4" />
                )}
              </Button>
            )}
            {config.expandTrigger === "header" && (
              <>
                {isExpanded ? (
                  <ChevronDown className="h-5 w-5" />
                ) : (
                  <ChevronUp className="h-5 w-5" />
                )}
              </>
            )}
          </div>
        </div>

        {/* Expanded Content */}
        {isExpanded && (
          <>
            {/* Messages */}
            <div 
              className="flex-1 overflow-y-auto p-4"
              style={{ 
                height: `calc(${config.expandedHeight} - 140px)` 
              }}
            >
              <div className="space-y-4">
                {messages.map((message, index) => (
                  <div
                    key={index}
                    className={cn(
                      "p-3 rounded-lg",
                      message.sender === "support"
                        ? "bg-muted"
                        : "bg-primary/10 ml-8"
                    )}
                  >
                    <div className="flex justify-between items-center mb-1">
                      <p className="text-sm font-semibold">
                        {message.sender === "support" ? "Support" : "You"}
                      </p>
                      <span className="text-xs text-muted-foreground">
                        {formatTime(message.timestamp)}
                      </span>
                    </div>
                    <p className="text-sm">{message.content}</p>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Input */}
            <div className="p-4 border-t border-border">
              <div className="flex gap-2">
                <Input
                  type="text"
                  placeholder={config.placeholder}
                  className="flex-1"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                />
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim()}
                  size="icon"
                >
                  <Send size={18} />
                </Button>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Backdrop when expanded */}
      {config.backdropOnExpand && isExpanded && (
        <div
          className="fixed inset-0 bg-black/20 z-40"
          style={{
            transitionDuration: `${config.animationDuration}ms`,
          }}
          onClick={() => setIsExpanded(false)}
        />
      )}
    </>
  );
}
