"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send, ChevronUp, ChevronDown, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface Message {
  sender: "user" | "support";
  content: string;
  timestamp: string;
}

interface AutomatedResponse {
  keywords: string[];
  response: string;
}

interface ChatConfig {
  title: string;
  subtitle?: string;
  placeholder: string;
  sendButtonText: string;
  initialMessages: Message[];
  automatedResponses: AutomatedResponse[];
  defaultResponse: string;
}

interface ResponsiveChatRendererProps {
  config: ChatConfig;
  className?: string;
  style?: React.CSSProperties;
}

export default function ResponsiveChatRenderer({
  config,
  className,
  style,
}: ResponsiveChatRendererProps) {
  const [messages, setMessages] = useState<Message[]>(
    config.initialMessages || []
  );
  const [inputValue, setInputValue] = useState("");
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Check if we're on mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Add bottom padding to body when mobile chat is present
  useEffect(() => {
    if (isMobile) {
      document.body.style.paddingBottom = isExpanded ? "0px" : "80px";
    } else {
      document.body.style.paddingBottom = "0px";
    }

    return () => {
      document.body.style.paddingBottom = "0px";
    };
  }, [isMobile, isExpanded]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      sender: "user",
      content: inputValue,
      timestamp: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");

    // Simulate response after a short delay
    setTimeout(() => {
      const responseMessage: Message = {
        sender: "support",
        content: getAutomatedResponse(inputValue),
        timestamp: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, responseMessage]);
    }, 1000);
  };

  const getAutomatedResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    // Check for keyword matches in automated responses
    for (const responseObj of config.automatedResponses) {
      if (
        responseObj.keywords.some((keyword) =>
          input.includes(keyword.toLowerCase())
        )
      ) {
        return responseObj.response;
      }
    }

    // Return default response if no keywords match
    return config.defaultResponse;
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // Mobile layout
  if (isMobile) {
    return (
      <>
        {/* Mobile Chat - Bottom Bar */}
        <div
          className={cn(
            "fixed bottom-0 left-0 right-0 z-50 bg-card border-t border-border",
            "transition-all duration-300 ease-in-out",
            isExpanded ? "h-[80vh]" : "h-auto"
          )}
        >
          {/* Header - Always Visible */}
          <div
            className="p-4 border-b border-border cursor-pointer flex items-center justify-between"
            onClick={toggleExpanded}
          >
            <div className="flex-1">
              <h2 className="font-bold text-lg">{config.title}</h2>
              {config.subtitle && !isExpanded && (
                <p className="text-xs text-muted-foreground">
                  {config.subtitle}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              {messages.length > config.initialMessages.length &&
                !isExpanded && (
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-primary font-medium">
                      {messages.length - config.initialMessages.length} new
                    </span>
                    <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                  </div>
                )}
              {isExpanded ? (
                <ChevronDown className="h-5 w-5" />
              ) : (
                <ChevronUp className="h-5 w-5" />
              )}
            </div>
          </div>

          {/* Expanded Content */}
          {isExpanded && (
            <>
              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 h-[calc(80vh-140px)]">
                <div className="space-y-4">
                  {messages.map((message, index) => (
                    <div
                      key={index}
                      className={cn(
                        "p-3 rounded-lg",
                        message.sender === "support"
                          ? "bg-muted"
                          : "bg-primary/10 ml-8"
                      )}
                    >
                      <div className="flex justify-between items-center mb-1">
                        <p className="text-sm font-semibold">
                          {message.sender === "support" ? "Support" : "You"}
                        </p>
                        <span className="text-xs text-muted-foreground">
                          {formatTime(message.timestamp)}
                        </span>
                      </div>
                      <p className="text-sm">{message.content}</p>
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              </div>

              {/* Input */}
              <div className="p-4 border-t border-border">
                <div className="flex gap-2">
                  <Input
                    type="text"
                    placeholder={config.placeholder}
                    className="flex-1"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim()}
                    size="icon"
                  >
                    <Send size={18} />
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Backdrop when expanded */}
        {isExpanded && (
          <div
            className="fixed inset-0 bg-black/20 z-40"
            onClick={() => setIsExpanded(false)}
          />
        )}
      </>
    );
  }

  // Desktop layout (original)
  return (
    <div
      className={cn("bg-card h-full flex flex-col", className)}
      style={style}
    >
      <div className="p-4 border-b border-border">
        <h2 className="font-bold text-lg">{config.title}</h2>
        {config.subtitle && (
          <p className="text-xs text-muted-foreground">{config.subtitle}</p>
        )}
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {messages.map((message, index) => (
            <div
              key={index}
              className={cn(
                "p-3 rounded-lg",
                message.sender === "support" ? "bg-muted" : "bg-primary/10 ml-8"
              )}
            >
              <div className="flex justify-between items-center mb-1">
                <p className="text-sm font-semibold">
                  {message.sender === "support" ? "Support" : "You"}
                </p>
                <span className="text-xs text-muted-foreground">
                  {formatTime(message.timestamp)}
                </span>
              </div>
              <p className="text-sm">{message.content}</p>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <Input
            type="text"
            placeholder={config.placeholder}
            className="flex-1"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <Button onClick={handleSendMessage} disabled={!inputValue.trim()}>
            {config.sendButtonText || <Send size={18} />}
          </Button>
        </div>
      </div>
    </div>
  );
}
