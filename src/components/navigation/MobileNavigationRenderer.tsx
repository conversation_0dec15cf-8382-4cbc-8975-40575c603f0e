"use client";

import * as React from "react";
import Link from "next/link";
import { Menu, X, ChevronDown, ChevronRight } from "lucide-react";

import { cn, capitalizeFirstLetter } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  <PERSON>et<PERSON>ontent,
  Sheet<PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { NavigationItem } from "@/types";
import * as LucideIcons from "lucide-react";

interface MobileNavigationRendererProps {
  items: NavigationItem[];
}

export function MobileNavigationRenderer({ items }: MobileNavigationRendererProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  // Function to dynamically render icons
  const renderIcon = (iconName?: string) => {
    if (!iconName) return null;

    // @ts-ignore - LucideIcons has dynamic keys
    const IconComponent = LucideIcons[capitalizeFirstLetter(iconName)];

    if (!IconComponent) {
      console.warn(`Icon "${iconName}" not found`);
      return null;
    }

    return <IconComponent className="mr-3 h-5 w-5 text-foreground" />;
  };

  const handleLinkClick = () => {
    setIsOpen(false);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden"
          aria-label="Open navigation menu"
        >
          <Menu className="h-6 w-6" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[300px] sm:w-[400px]">
        <SheetHeader>
          <SheetTitle className="text-left">Navigation</SheetTitle>
        </SheetHeader>
        <nav className="mt-6">
          <ul className="space-y-2">
            {items.map((item) => (
              <MobileNavigationItem
                key={item.id}
                item={item}
                renderIcon={renderIcon}
                onLinkClick={handleLinkClick}
              />
            ))}
          </ul>
        </nav>
      </SheetContent>
    </Sheet>
  );
}

interface MobileNavigationItemProps {
  item: NavigationItem;
  renderIcon: (iconName?: string) => React.ReactNode;
  onLinkClick: () => void;
}

function MobileNavigationItem({
  item,
  renderIcon,
  onLinkClick,
}: MobileNavigationItemProps) {
  const [isExpanded, setIsExpanded] = React.useState(false);
  const hasSubItems = item.items && item.items.length > 0;

  if (!hasSubItems) {
    return (
      <li>
        <Link
          href={item.link}
          onClick={onLinkClick}
          className={cn(
            "flex items-center py-3 px-4 rounded-lg text-sm font-medium transition-colors",
            "hover:bg-accent hover:text-accent-foreground",
            "focus:bg-accent focus:text-accent-foreground focus:outline-none"
          )}
        >
          {renderIcon(item.icon)}
          {item.label}
        </Link>
      </li>
    );
  }

  return (
    <li>
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <button
            className={cn(
              "flex items-center justify-between w-full py-3 px-4 rounded-lg text-sm font-medium transition-colors",
              "hover:bg-accent hover:text-accent-foreground",
              "focus:bg-accent focus:text-accent-foreground focus:outline-none"
            )}
          >
            <div className="flex items-center">
              {renderIcon(item.icon)}
              {item.label}
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </button>
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2">
          <ul className="ml-6 space-y-1">
            {item.items?.map((subItem) => (
              <li key={subItem.id}>
                <Link
                  href={subItem.link}
                  onClick={onLinkClick}
                  className={cn(
                    "block py-2 px-4 rounded-md text-sm transition-colors",
                    "hover:bg-accent hover:text-accent-foreground",
                    "focus:bg-accent focus:text-accent-foreground focus:outline-none"
                  )}
                >
                  <div className="font-medium">{subItem.label}</div>
                  {subItem.description && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {subItem.description}
                    </div>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </CollapsibleContent>
      </Collapsible>
    </li>
  );
}
