import * as React from "react";
import Link from "next/link";

import { cn, capitalizeFirstLetter } from "@/lib/utils";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { NavigationItem } from "@/types";
import * as LucideIcons from "lucide-react";

interface NavigationRendererProps {
  items: NavigationItem[];
}

export function NavigationRenderer({ items }: NavigationRendererProps) {
  // Function to dynamically render icons
  const renderIcon = (iconName?: string) => {
    if (!iconName) return null;

    // @ts-ignore - LucideIcons has dynamic keys
    const IconComponent = LucideIcons[capitalizeFirstLetter(iconName)];

    if (!IconComponent) {
      console.warn(`Icon "${iconName}" not found`);
      return null;
    }

    return <IconComponent className="mr-2 h-4 w-4 text-foreground" />;
  };

  return (
    <NavigationMenu>
      <NavigationMenuList>
        {items.map((item) => (
          <NavigationMenuItem key={item.id}>
            {item.items && item.items.length > 0 ? (
              <>
                <NavigationMenuTrigger>
                  {renderIcon(item.icon)}
                  {item.label}
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                    {item.items.map((subItem) => (
                      <ListItem
                        key={subItem.id}
                        title={subItem.label}
                        href={subItem.link}
                      >
                        {subItem.description}
                      </ListItem>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </>
            ) : (
              <NavigationMenuLink
                className={navigationMenuTriggerStyle()}
                asChild
              >
                <Link
                  href={item.link}
                  className={`${item.icon ? "flex flex-row items-center" : ""}`}
                >
                  {renderIcon(item.icon)}
                  {item.label}
                </Link>
              </NavigationMenuLink>
            )}
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
}

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a">
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  );
});
ListItem.displayName = "ListItem";
