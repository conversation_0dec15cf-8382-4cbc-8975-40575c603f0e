"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Send } from "lucide-react";

interface Message {
  sender: "user" | "support";
  content: string;
  timestamp: string;
}

export default function ChatSidebar() {
  const [messages, setMessages] = useState<Message[]>([
    {
      sender: "support",
      content: "Hello! How can I help you today?",
      timestamp: new Date().toISOString(),
    },
  ]);
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      sender: "user",
      content: inputValue,
      timestamp: new Date().toISOString(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");

    // Simulate response after a short delay
    setTimeout(() => {
      const responseMessage: Message = {
        sender: "support",
        content: getAutomatedResponse(inputValue),
        timestamp: new Date().toISOString(),
      };
      setMessages((prev) => [...prev, responseMessage]);
    }, 1000);
  };

  const getAutomatedResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    if (
      input.includes("appointment") ||
      input.includes("schedule") ||
      input.includes("book")
    ) {
      return "I'd be happy to help you schedule an appointment. Please call us at (************* or use our online booking form on the contact page.";
    } else if (input.includes("hours") || input.includes("open")) {
      return "Our office hours are Monday-Friday 8am-5pm and Saturday 9am-2pm. We're closed on Sundays.";
    } else if (input.includes("insurance") || input.includes("payment")) {
      return "We accept most major insurance plans and offer various payment options. Would you like more specific information?";
    } else if (input.includes("emergency")) {
      return "If you're experiencing a dental emergency, please call us immediately at (*************. For after-hours emergencies, our answering service will connect you with the on-call dentist.";
    } else if (input.includes("service") || input.includes("treatment")) {
      return "We offer a wide range of dental services including preventive care, restorative treatments, cosmetic procedures, and more. Is there a specific service you're interested in?";
    } else {
      return "Thank you for your message. How else can I assist you with your dental care needs?";
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <div className="bg-card h-full flex flex-col border-l border-border">
      <div className="p-4 border-b border-border">
        <h2 className="font-bold text-lg">Chat Support</h2>
        <p className="text-xs text-muted-foreground">
          We typically reply within minutes
        </p>
      </div>

      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`${
                message.sender === "support"
                  ? "bg-muted p-3 rounded-lg"
                  : "bg-primary/10 p-3 rounded-lg ml-8"
              }`}
            >
              <div className="flex justify-between items-center mb-1">
                <p className="text-sm font-semibold">
                  {message.sender === "support" ? "Support" : "You"}
                </p>
                <span className="text-xs text-muted-foreground">
                  {formatTime(message.timestamp)}
                </span>
              </div>
              <p className="text-sm">{message.content}</p>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <Input
            type="text"
            placeholder="Type a message..."
            className="flex-1 p-2 border border-input rounded-md"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <Button
            className="bg-primary text-primary-foreground px-4 py-2 rounded-md"
            onClick={handleSendMessage}
            disabled={!inputValue.trim()}
          >
            <Send size={18} />
          </Button>
        </div>
      </div>
    </div>
  );
}
