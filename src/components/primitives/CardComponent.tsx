import { Component } from "@/types";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "../ui/card";
import ComponentRenderer from "../renderer/ComponentRenderer";

interface CardComponentProps {
  title?: string;
  description?: string;
  content: Component[];
  footer?: Component[];
  className?: string;
}

export async function CardComponent(card: CardComponentProps) {
  if (!card.title && !card.content) return null;

  return (
    <Card className={card.className || ""}>
      {card.title && (
        <CardHeader>
          <CardTitle>{card.title}</CardTitle>
          {card.description && (
            <CardDescription>{card.description}</CardDescription>
          )}
        </CardHeader>
      )}
      <CardContent>
        {card.content.map((component, index) => (
          <ComponentRenderer
            key={`${component.type}-${index}`}
            data={component}
          />
        ))}
      </CardContent>
      {card.footer && (
        <CardFooter>
          {card.footer.map((component, index) => (
            <ComponentRenderer
              key={`${component.type}-${index}`}
              data={component}
            />
          ))}
        </CardFooter>
      )}
    </Card>
  );
}
