import { cn } from "@/lib/utils";
import Link from "next/link";
import Image from "next/image";

interface ImageComponentProps {
  src: string;
  alt: string;
  link?: string;
  width: number;
  height: number;
  className?: string;
}

export async function ImageComponent(image: ImageComponentProps) {
  if (!image.src) return null;

  return (
    <>
      {image.link ? (
        <Link href={image.link}>
          <Image
            src={image.src}
            alt={image.alt || "Image"}
            width={image.width || 150}
            height={image.height || 40}
            className={cn(
              "hover:opacity-80 transition-opacity",
              image.className || ""
            )}
          />
        </Link>
      ) : (
        <Image
          src={image.src}
          alt={image.alt || "Image"}
          width={image.width || 150}
          height={image.height || 40}
          className={image.className || ""}
        />
      )}
    </>
  );
}
