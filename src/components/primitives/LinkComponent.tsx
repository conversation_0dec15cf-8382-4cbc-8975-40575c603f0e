import Link from "next/link";

interface LinkComponentProps {
  href: string;
  content: string;
  className?: string;
  external?: boolean;
}

export async function LinkComponent(link: LinkComponentProps) {
  if (!link.href) return null;

  return (
    <Link
      href={link.href || "#"}
      className={link.className}
      target={link.external ? "_blank" : undefined}
      rel={link.external ? "noopener noreferrer" : undefined}
    >
      {link.content}
    </Link>
  );
}
