interface HeaderComponentProps {
  content: string;
  type: "h1" | "h2" | "h3" | "h4" | "h5" | "h6";
  className?: string;
}

export function HeaderComponent({
  content,
  type,
  className = "",
}: HeaderComponentProps) {
  if (!content) return null;

  switch (type) {
    case "h1":
      return <h1 className={className}>{content}</h1>;
    case "h2":
      return <h2 className={className}>{content}</h2>;
    case "h3":
      return <h3 className={className}>{content}</h3>;
    case "h4":
      return <h4 className={className}>{content}</h4>;
    case "h5":
      return <h5 className={className}>{content}</h5>;
    case "h6":
      return <h6 className={className}>{content}</h6>;
    default:
      return <h2 className={className}>{content}</h2>;
  }
}
