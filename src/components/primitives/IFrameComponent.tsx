interface IFrameComponentProps {
  src: string;
  title?: string;
  width?: string;
  height?: number;
  className?: string;
  allowFullScreen?: boolean;
}

export async function IFrameComponent(iframe: IFrameComponentProps) {
  if (!iframe.src) return null;

  return (
    <iframe
      src={iframe.src}
      title={iframe.title || "Embedded content"}
      width={iframe.width || "100%"}
      height={iframe.height || 300}
      className={iframe.className}
      frameBorder="0"
      allowFullScreen={iframe.allowFullScreen}
      loading="lazy"
    />
  );
}
