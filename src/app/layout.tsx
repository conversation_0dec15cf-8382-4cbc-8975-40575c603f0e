import type { <PERSON>ada<PERSON> } from "next";
import { Architects_Daughter } from "next/font/google";
import "./globals.css";
import ThemeProvider from "@/components/theme/ThemeProvider";

const architectsDaughter = Architects_Daughter({
  weight: "400",
  variable: "--font-architects-daughter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "GraphQL Theme Demo",
  description: "Demo of GraphQL theme integration",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${architectsDaughter.variable}`}>
        <ThemeProvider />
        {children}
      </body>
    </html>
  );
}
