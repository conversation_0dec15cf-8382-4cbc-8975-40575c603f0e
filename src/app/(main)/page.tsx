import Image from "next/image";
import { Button } from "@/components/ui/button";

export default async function Home() {
  return (
    <div className="min-h-screen p-8 bg-background text-foreground">
      <header className="mb-8 max-w-4xl mx-auto">
        <h1>GraphQL Theme Demo</h1>
        <p>
          This page demonstrates the theme fetched from GraphQL and applied to
          the layout
        </p>
      </header>

      <main className="mb-8 max-w-4xl mx-auto"></main>

      <section className="mb-8 max-w-4xl mx-auto">
        <h2>Theme Elements Preview</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div className="p-6 bg-card text-card-foreground rounded-lg shadow-md border">
            <h3 className="text-lg font-semibold mb-4">Typography</h3>
            <h1 className="text-3xl font-bold mb-2">Heading 1</h1>
            <h2 className="text-2xl font-semibold mb-2">Heading 2</h2>
            <h3 className="text-xl font-medium mb-4">Heading 3</h3>
            <p className="text-muted-foreground mb-4">
              This is a paragraph with normal text. The styling is applied from
              the theme.
            </p>
            <a href="#" className="text-primary hover:underline">
              This is a link styled with the theme
            </a>
          </div>

          <div className="p-6 bg-card text-card-foreground rounded-lg shadow-md border">
            <h3 className="text-lg font-semibold mb-4">Buttons</h3>
            <div className="flex flex-col gap-4 mt-4">
              <Button variant="default">Primary Button</Button>
              <Button variant="secondary">Secondary Button</Button>
              <Button variant="outline">Outlined Button</Button>
            </div>
          </div>
        </div>

        <div className="mt-6 p-6 bg-card text-card-foreground rounded-lg shadow-md border">
          <h3 className="text-lg font-semibold mb-4">Spacing & Borders</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="p-4 border-2 border-primary rounded-md text-center">
              Primary Border
            </div>
            <div className="p-4 border-2 border-secondary rounded-md text-center">
              Secondary Border
            </div>
            <div className="p-4 border-2 border-accent rounded-md text-center">
              Accent Border
            </div>
          </div>

          <div className="mt-6">
            <h4 className="text-lg font-semibold mb-4">Font Examples</h4>
            <p className="font-sans text-muted-foreground mb-2">
              This text uses the theme's sans-serif font: {`var(--font-sans)`}
            </p>
            <p className="font-mono text-muted-foreground">
              This text uses the theme's monospace font: {`var(--font-mono)`}
            </p>
          </div>
        </div>
      </section>

      <footer className="flex gap-[24px] flex-wrap items-center justify-center max-w-4xl mx-auto">
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/file.svg"
            alt="File icon"
            width={16}
            height={16}
          />
          Learn
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/window.svg"
            alt="Window icon"
            width={16}
            height={16}
          />
          Examples
        </a>
      </footer>
    </div>
  );
}
