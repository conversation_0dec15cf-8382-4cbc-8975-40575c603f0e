import LayoutRenderer from "@/components/layouts/LayoutRenderer";
import { detectDeviceTypeEnhanced } from "@/lib/utils/deviceDetection";
import { detectBreakpoint } from "@/lib/utils/breakpointUtils";

export default async function MainLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Detect device type and screen size on the server side
  const { deviceType, screenHints } = await detectDeviceTypeEnhanced();

  // Determine the appropriate breakpoint based on device type and screen size
  const breakpoint = await detectBreakpoint(deviceType, screenHints.width);

  return (
    <LayoutRenderer layoutId="modernLayout" breakpoint={breakpoint}>
      {children}
    </LayoutRenderer>
  );
}
