import { notFound } from "next/navigation";
import { Metadata } from "next";
import { fetchPageByPath, generateStaticPaths } from "@/lib/api/page.service";
import PageRenderer from "@/components/renderer/PageRenderer";

interface PageProps {
  params: {
    slug?: string[];
  };
}

// Generate metadata for the page
export async function generateMetadata({
  params,
}: PageProps): Promise<Metadata> {
  const { slug } = await params;
  const path = slug ? `/${slug.join("/")}` : "/";
  const page = await fetchPageByPath(path);

  if (!page) {
    return {
      title: "Page Not Found",
    };
  }

  return {
    title: page.title,
    description: `${page.title} - JStreet Dental Group`,
  };
}

// Generate static params for all pages
export async function generateStaticParams() {
  return await generateStaticPaths();
}

export default async function DynamicPage({ params }: PageProps) {
  const { slug } = await params;
  // Construct the path from the slug
  const path = slug ? `/${slug.join("/")}` : "/";

  // Fetch the page data
  const page = await fetchPageByPath(path);

  if (!page) {
    notFound();
  }

  return (
    <main className="min-h-screen">
      <PageRenderer page={page} />
    </main>
  );
}
