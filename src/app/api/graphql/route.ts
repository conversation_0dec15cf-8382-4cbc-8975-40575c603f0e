import { NextRequest, NextResponse } from 'next/server';

// Mock response data is commented out as we're using a real API now
// const mockWebsiteResponse = { ... };

export async function POST(request: NextRequest) {
  try {
    // Parse the incoming request body
    const body = await request.json();

    // Forward the request to the external GraphQL API
    const response = await fetch('https://hafez.dentalink.dev/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add any authentication headers if needed
      },
      body: JSON.stringify(body),
    });
    
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('GraphQL proxy error:', error);

    // Return an error response
    return NextResponse.json(
      {
        errors: [{ message: 'Error processing GraphQL request' }]
      },
      { status: 500 }
    );
  }
}
