import { NextRequest, NextResponse } from "next/server";

// This is a mock API endpoint that simulates form submission
export async function POST(request: NextRequest) {
  try {
    // Parse the incoming request body
    const body = await request.json();

    // Log the form data (for development purposes)
    console.log("Form submission received:", body);

    // Simulate processing delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Randomly succeed or fail (80% success rate) for testing error handling
    const shouldSucceed = Math.random() < 0.8;

    if (!shouldSucceed) {
      // Simulate a server error
      return NextResponse.json(
        { error: "Server error occurred" },
        { status: 500 }
      );
    }

    // Return a successful response
    return NextResponse.json(
      {
        success: true,
        message: "Form submitted successfully",
        data: body,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Form submission error:", error);

    // Return an error response
    return NextResponse.json(
      { error: "Error processing form submission" },
      { status: 500 }
    );
  }
}
