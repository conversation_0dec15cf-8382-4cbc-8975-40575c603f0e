export interface GlobalSettings {
  theme: Theme;
  defaultLayoutId: string;
  breakpoints: {
    [key: string]: string; // e.g. "mobile": "0px"
  };
}

export interface Theme {
  light: ThemeColors;
  dark: ThemeColors;
  fonts: {
    sans: string;
    serif: string;
    mono: string;
  };
  radius: string;
  trackingNormal: string;
  shadows: {
    "2xs": string;
    xs: string;
    sm: string;
    default: string;
    md: string;
    lg: string;
    xl: string;
    "2xl": string;
  };
}

export interface ThemeColors {
  background: string;
  foreground: string;
  card: string;
  cardForeground: string;
  popover: string;
  popoverForeground: string;
  primary: string;
  primaryForeground: string;
  secondary: string;
  secondaryForeground: string;
  muted: string;
  mutedForeground: string;
  accent: string;
  accentForeground: string;
  destructive: string;
  destructiveForeground: string;
  border: string;
  input: string;
  ring: string;
  chart1: string;
  chart2: string;
  chart3: string;
  chart4: string;
  chart5: string;
  sidebar: string;
  sidebarForeground: string;
  sidebarPrimary: string;
  sidebarPrimaryForeground: string;
  sidebarAccent: string;
  sidebarAccentForeground: string;
  sidebarBorder: string;
  sidebarRing: string;
}
