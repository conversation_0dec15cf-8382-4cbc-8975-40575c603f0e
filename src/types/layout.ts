export interface Layout {
  id: string;
  description: string;
  type: string;
  config: LayoutConfig;
  styles: Record<string, string>;
  components: LayoutComponent[];
  mobileComponents?: LayoutComponent[];
}

export interface LayoutComponent {
  componentId: string;
  area: string;
}

export interface LayoutConfig {
  default: BreakpointConfig;
  [breakpoint: string]: BreakpointConfig | undefined; // For responsive breakpoints like "mobile", "tablet"
}

export interface BreakpointConfig {
  areas: string[];
  rows: string[];
  columns: string[];
  gap: string;
}
