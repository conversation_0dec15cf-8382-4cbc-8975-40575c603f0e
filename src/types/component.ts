// Component related types
export interface Component {
  id: string;
  type: string;
  config: ComponentConfig;
  styles?: Record<string, string>;
}

export interface ComponentConfig {
  [key: string]: any; // Generic config properties
}

// Specific component configs
export interface ImageConfig extends ComponentConfig {
  src: string;
  altText: string;
  link?: string;
}

export interface NavigationMenuConfig extends ComponentConfig {
  orientation: "horizontal" | "vertical";
  items: NavigationItem[];
}

export interface NavigationItem {
  id: string;
  icon?: string;
  label: string;
  description?: string;
  link: string;
  items?: NavigationItem[];
}

export interface TextBlockConfig extends ComponentConfig {
  content: string;
  textAlign?: string;
}
