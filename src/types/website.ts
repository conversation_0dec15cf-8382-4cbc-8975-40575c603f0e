import { Component } from "react";
import { Layout } from "./layout";
import { Page } from "./page";

// Core website types
export interface Website {
  id: string;
  name: string;
  domain: string;
  globalSettings: GlobalSettings;
  layouts: Layout[];
  components: Component[];
  pages: Page[];
}

export interface GlobalSettings {
  theme: Theme;
  defaultLayoutId: string;
  breakpoints: {
    [key: string]: string; // e.g. "mobile": "0px"
  };
}

export interface Theme {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  backgroundColor: string;
  textColor: string;
  defaultFont: Font;
  headingFont: Font;
  spacingUnit: string;
}

export interface Font {
  family: string;
  weights: {
    normal: number;
    bold: number;
  };
}
