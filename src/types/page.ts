// Page related types
export interface Page {
  id: string;
  path: string;
  title: string;
  layoutId: string;
  elements: PageElement[];
}

export interface PageElement {
  componentId?: string;
  type?: string;
  area?: string;
  layout?: {
    type: string;
    config: {
      direction?: string;
      gap?: string;
      [breakpoint: string]: any;
    };
  };
  elements?: PageElement[];
  config?: Record<string, any>;
  styles?: Record<string, string>;
}
