import { gql } from '@apollo/client';

export const GET_WEBSITE_BY_ID = gql`
  query GetWebsiteById($id: ID!) {
    websiteById(id: $id) {
      id
      name
      pages(first: 1) {
        nodes {
          title
          slug
        }
      }
      navigationMenus(first: 1) {
        nodes {
          name
          items {
            nodes {
              id
              label
              children {
                nodes {
                  label
                }
              }
            }
          }
        }
      }
      theme {
        borders
        colors
        createdAt
        description
        effects
        id
        lastModifiedAt
        name
        shadows
        spacing
        typography
        layoutConfig {
          breakpointLayouts {
            key
            value {
              customName
              displayName
              gridArea
              gridColumnEnd
              gridColumnStart
              gridRowEnd
              gridRowStart
              height
              isVisible
              order
              section
              width
            }
          }
          gridTemplateAreas {
            key
            value
          }
        }
      }
    }
  }
`;
