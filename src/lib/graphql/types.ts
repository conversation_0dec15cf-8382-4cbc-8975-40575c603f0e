export interface Website {
  id: string;
  name: string;
  pages: {
    nodes: {
      title: string;
      slug: string;
    }[];
  };
  navigationMenus: {
    nodes: {
      name: string;
      items: {
        nodes: {
          id: string;
          label: string;
          children: {
            nodes: {
              label: string;
            }[];
          };
        }[];
      };
    }[];
  };
  theme: Theme;
}

export interface Theme {
  borders: string;
  colors: string;
  createdAt: string;
  description: string;
  effects: string;
  id: string;
  lastModifiedAt: string | null;
  name: string;
  shadows: string;
  spacing: string;
  typography: string;
  layoutConfig?: string | ParsedLayoutConfig;
}

export interface ParsedTheme {
  borders: {
    width: {
      thin: string;
      medium: string;
      thick: string;
    };
    radius: {
      sm: string;
      md: string;
      lg: string;
      xl: string;
      round: string;
    };
  };
  colors: {
    text: string;
    heading: string;
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    lightGray: string;
    darkGray: string;
  };
  effects: {
    hover: {
      scale: string;
      lift: string;
      glow: string;
    };
    transition: {
      fast: string;
      medium: string;
      slow: string;
    };
  };
  shadows: {
    small: string;
    medium: string;
    large: string;
    xl: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
    xxl: string;
    section: string;
  };
  typography: {
    bodyFont: string;
    headingFont: string;
    baseFontSize: string;
    body: {
      fontSize: string;
      fontWeight: string;
      lineHeight: string;
    };
    h1: {
      fontSize: string;
      fontWeight: string;
      lineHeight: string;
    };
    h2: {
      fontSize: string;
      fontWeight: string;
      lineHeight: string;
    };
    h3: {
      fontSize: string;
      fontWeight: string;
      lineHeight: string;
    };
  };
  layoutConfig?: ParsedLayoutConfig;
}

export interface ParsedLayoutConfigBreakpointValue {
  customName?: string;
  displayName?: string;
  gridArea?: string;
  gridColumnEnd?: string;
  gridColumnStart?: string;
  gridRowEnd?: string;
  gridRowStart?: string;
  height?: string;
  isVisible?: boolean;
  order?: number;
  section?: string;
  width?: string;
}

export interface ParsedLayoutConfig {
  breakpointLayouts: {
    key: string;
    value: ParsedLayoutConfigBreakpointValue;
  }[];
  gridTemplateAreas: {
    key: string;
    value: string;
  }[];
}
