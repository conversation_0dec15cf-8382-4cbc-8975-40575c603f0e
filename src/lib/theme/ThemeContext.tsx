'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { ParsedTheme } from '../graphql/types';
import { createCssVariables, applyThemeToElement } from './themeUtils';

interface ThemeContextType {
  theme: ParsedTheme | null;
  setTheme: (theme: ParsedTheme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ 
  children,
  initialTheme = null
}: { 
  children: React.ReactNode;
  initialTheme?: ParsedTheme | null;
}) {
  const [theme, setThemeState] = useState<ParsedTheme | null>(initialTheme);

  const setTheme = (newTheme: ParsedTheme) => {
    setThemeState(newTheme);
  };

  useEffect(() => {
    if (theme) {
      const variables = createCssVariables(theme);
      
      // Apply theme to document root
      const root = document.documentElement;
      applyThemeToElement(root, variables);
    }
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
