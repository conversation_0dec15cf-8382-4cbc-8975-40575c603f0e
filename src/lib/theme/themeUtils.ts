import { Theme, ParsedTheme, ParsedLayoutConfig } from '../graphql/types';

export function parseTheme(theme: Theme): ParsedTheme {
  const borders = JSON.parse(theme.borders);
  const colors = JSON.parse(theme.colors);
  const effects = JSON.parse(theme.effects);
  const shadows = JSON.parse(theme.shadows);
  const spacing = JSON.parse(theme.spacing);
  const typography = JSON.parse(theme.typography);
  
  let layoutConfig: ParsedLayoutConfig | undefined = undefined;
  if (theme.layoutConfig) {
    if (typeof theme.layoutConfig === 'string') {
      layoutConfig = JSON.parse(theme.layoutConfig);
    } else {
      // It's already an object, assume it's ParsedLayoutConfig
      layoutConfig = theme.layoutConfig as ParsedLayoutConfig;
    }
  }

  return {
    borders,
    colors,
    effects,
    shadows,
    spacing,
    typography,
    layoutConfig
  };
}

export function createCssVariables(parsedTheme: ParsedTheme): Record<string, string> {
  const variables: Record<string, string> = {};

  // Colors
  Object.entries(parsedTheme.colors).forEach(([key, value]) => {
    variables[`--color-${key}`] = value;
  });

  // Typography
  variables['--font-body'] = parsedTheme.typography.bodyFont;
  variables['--font-heading'] = parsedTheme.typography.headingFont;
  variables['--font-size-base'] = parsedTheme.typography.baseFontSize;
  
  variables['--font-size-body'] = parsedTheme.typography.body.fontSize;
  variables['--font-weight-body'] = parsedTheme.typography.body.fontWeight;
  variables['--line-height-body'] = parsedTheme.typography.body.lineHeight;
  
  variables['--font-size-h1'] = parsedTheme.typography.h1.fontSize;
  variables['--font-weight-h1'] = parsedTheme.typography.h1.fontWeight;
  variables['--line-height-h1'] = parsedTheme.typography.h1.lineHeight;
  
  variables['--font-size-h2'] = parsedTheme.typography.h2.fontSize;
  variables['--font-weight-h2'] = parsedTheme.typography.h2.fontWeight;
  variables['--line-height-h2'] = parsedTheme.typography.h2.lineHeight;
  
  variables['--font-size-h3'] = parsedTheme.typography.h3.fontSize;
  variables['--font-weight-h3'] = parsedTheme.typography.h3.fontWeight;
  variables['--line-height-h3'] = parsedTheme.typography.h3.lineHeight;

  // Spacing
  Object.entries(parsedTheme.spacing).forEach(([key, value]) => {
    variables[`--spacing-${key}`] = value;
  });

  // Borders
  Object.entries(parsedTheme.borders.width).forEach(([key, value]) => {
    variables[`--border-width-${key}`] = value;
  });
  
  Object.entries(parsedTheme.borders.radius).forEach(([key, value]) => {
    variables[`--border-radius-${key}`] = value;
  });

  // Shadows
  Object.entries(parsedTheme.shadows).forEach(([key, value]) => {
    variables[`--shadow-${key}`] = value;
  });

  // Effects
  Object.entries(parsedTheme.effects.transition).forEach(([key, value]) => {
    variables[`--transition-${key}`] = value;
  });

  return variables;
}

export function applyThemeToElement(element: HTMLElement, variables: Record<string, string>): void {
  Object.entries(variables).forEach(([key, value]) => {
    element.style.setProperty(key, value);
  });
}

export function createThemeStyleElement(variables: Record<string, string>): HTMLStyleElement {
  const style = document.createElement('style');
  
  let css = ':root {\n';
  Object.entries(variables).forEach(([key, value]) => {
    css += `  ${key}: ${value};\n`;
  });
  css += '}\n\n';

  // Add some basic theme styles
  css += `
body {
  font-family: var(--font-body);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-body);
  line-height: var(--line-height-body);
  color: var(--color-text);
  background-color: var(--color-background);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  color: var(--color-heading);
}

h1 {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-h1);
  line-height: var(--line-height-h1);
}

h2 {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-h2);
  line-height: var(--line-height-h2);
}

h3 {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-h3);
  line-height: var(--line-height-h3);
}

a {
  color: var(--color-primary);
  transition: var(--transition-fast);
}

a:hover {
  color: var(--color-accent);
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: var(--transition-medium);
}

.btn-primary:hover {
  background-color: var(--color-accent);
  box-shadow: var(--shadow-small);
}
`;

  style.textContent = css;
  return style;
}
