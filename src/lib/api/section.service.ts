import { Section } from "@/types";
import websiteData from "../../../data-clean.json";
import { fetchComponentsByIds } from "./component.service";
import { BreakpointName } from "@/lib/utils/breakpointUtils";

// Get sections from the website data
const mockSections = websiteData.website.sections || [];

/**
 * Fetches a section by ID with optional breakpoint-specific handling
 * @param sectionId The ID of the section to fetch
 * @param breakpoint The current breakpoint for responsive section selection
 * @returns The section data or null if not found
 */
export async function fetchSection(
  sectionId: string,
  breakpoint?: BreakpointName
): Promise<Section | null> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Handle responsive navigation sections
  let targetSectionId = sectionId;
  if (sectionId === "navigation" && breakpoint === "mobile") {
    // Use mobile navigation for mobile breakpoint
    targetSectionId = "mobileNavigation";
  }

  // Find the section in the mock data
  const sectionData = mockSections.find(
    (section) => section.id === targetSectionId
  );

  if (!sectionData) {
    return null;
  }

  // Fetch the components for this section
  const components = await fetchComponentsByIds(sectionData.componentIds || []);

  // Return the section with components
  return {
    id: sectionData.id,
    type: sectionData.type,
    config: sectionData.config,
    components,
  };
}

/**
 * Fetches multiple sections by their IDs
 * @param sectionIds Array of section IDs to fetch
 * @returns Array of found sections
 */
export async function fetchSectionsByIds(
  sectionIds: string[]
): Promise<Section[]> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 150));

  const sections: Section[] = [];

  for (const sectionId of sectionIds) {
    const section = await fetchSection(sectionId);
    if (section) {
      sections.push(section);
    }
  }

  return sections;
}
