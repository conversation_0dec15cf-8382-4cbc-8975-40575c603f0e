import { Theme, GlobalSettings } from "@/types/global-settings";
import websiteData from "../../../data-clean.json";

/**
 * Fetches the theme settings from the website data
 * @returns The theme settings
 */
export async function fetchThemeSettings(): Promise<Theme> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  // Return the theme from the website data
  return websiteData.website.globalSettings.theme;
}

/**
 * Fetches all global settings including theme, default layout, and breakpoints
 * @returns The global settings
 */
export async function fetchGlobalSettings(): Promise<GlobalSettings> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  // Return all global settings from the website data
  return websiteData.website.globalSettings;
}
