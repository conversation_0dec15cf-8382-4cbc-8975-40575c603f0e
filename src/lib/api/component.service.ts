import { Component } from "@/types";
import websiteData from "../../../data-clean.json";

export async function fetchComponentById(
  componentId: string
): Promise<Component | null> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 0));

  const componentData = websiteData.website.components.find(
    (component) => component.id === componentId
  );

  return componentData
    ? {
        ...componentData,
        styles: Object.fromEntries(
          Object.entries(componentData.styles || {}).filter(
            ([_, value]) => value !== undefined
          )
        ),
      }
    : null;
}

export async function fetchComponentsByIds(
  componentIds: string[]
): Promise<Component[]> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 100));

  const components: Component[] = [];

  for (const componentId of componentIds) {
    const component = await fetchComponentById(componentId);
    if (component) {
      components.push(component);
    }
  }

  return components;
}
