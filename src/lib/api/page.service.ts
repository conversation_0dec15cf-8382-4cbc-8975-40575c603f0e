import { Page } from "@/types";
import websiteData from "../../../data-clean.json";

// Mock page data - following the pattern of other services
const mockPages: Page[] = websiteData.website.pages;

export async function fetchPageByPath(path: string): Promise<Page | null> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 100));

  const page = mockPages.find((p) => p.path === path);

  return page || null;
}

export async function fetchPageById(pageId: string): Promise<Page | null> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 100));

  const page = mockPages.find((p) => p.id === pageId);

  return page || null;
}

export async function fetchAllPages(): Promise<Page[]> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 150));

  return mockPages;
}

// Generate static paths for Next.js static generation
export async function generateStaticPaths() {
  const pages = await fetchAllPages();

  return pages.map((page) => ({
    slug: page.path === "/" ? [] : page.path.split("/").filter(Boolean),
  }));
}
