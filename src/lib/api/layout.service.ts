import { Layout } from "@/types";
import websiteData from "../../../data-clean.json";

export async function fetchLayout(layoutId: string): Promise<Layout | null> {
  // Simulate network delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  const layoutData = websiteData.website.layouts.find(
    (layout) => layout.id === layoutId
  );

  if (layoutData) {
    return {
      id: layoutData.id,
      description: layoutData.description,
      type: layoutData.type,
      config: layoutData.config,
      styles: layoutData.styles || {},
      components: layoutData.components || [],
      mobileComponents: (layoutData as any).mobileComponents || [],
    };
  }

  return null;
}
