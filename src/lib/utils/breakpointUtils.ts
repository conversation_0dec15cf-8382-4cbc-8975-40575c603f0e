import { DeviceType } from "./deviceDetection";
import { fetchGlobalSettings } from "@/lib/api/theme.service";

export type BreakpointName = "mobile" | "tablet" | "desktop" | "default";

/**
 * Maps device type to breakpoint name based on the website's breakpoint configuration
 */
export function mapDeviceTypeToBreakpoint(deviceType: DeviceType): BreakpointName {
  switch (deviceType) {
    case "mobile":
      return "mobile";
    case "tablet":
      return "tablet";
    case "desktop":
      return "desktop";
    default:
      return "default";
  }
}

/**
 * Gets the appropriate breakpoint for a device type, considering the layout's available configurations
 */
export function getLayoutBreakpoint(
  deviceType: DeviceType,
  availableBreakpoints: string[]
): BreakpointName {
  const preferredBreakpoint = mapDeviceTypeToBreakpoint(deviceType);
  
  // Check if the preferred breakpoint exists in the layout configuration
  if (availableBreakpoints.includes(preferredBreakpoint)) {
    return preferredBreakpoint;
  }
  
  // Fallback logic: find the best available breakpoint
  if (deviceType === "mobile") {
    // For mobile, try tablet, then default
    if (availableBreakpoints.includes("tablet")) {
      return "tablet";
    }
  } else if (deviceType === "tablet") {
    // For tablet, try mobile or desktop, then default
    if (availableBreakpoints.includes("mobile")) {
      return "mobile";
    }
    if (availableBreakpoints.includes("desktop")) {
      return "desktop";
    }
  } else if (deviceType === "desktop") {
    // For desktop, try tablet, then default
    if (availableBreakpoints.includes("tablet")) {
      return "tablet";
    }
  }
  
  // Always fall back to default
  return "default";
}

/**
 * Gets breakpoint configuration from the website's global settings
 */
export async function getBreakpointConfig(): Promise<{
  [key: string]: string;
}> {
  try {
    const globalSettings = await fetchGlobalSettings();
    return globalSettings.breakpoints;
  } catch (error) {
    console.error("Failed to fetch breakpoint configuration:", error);
    // Return default breakpoints as fallback
    return {
      mobile: "0px",
      tablet: "768px",
      desktop: "1024px",
    };
  }
}

/**
 * Determines if a screen width matches a specific breakpoint
 */
export function matchesBreakpoint(
  screenWidth: number,
  breakpointValue: string
): boolean {
  // Parse the breakpoint value (e.g., "768px" -> 768)
  const breakpointWidth = parseInt(breakpointValue.replace(/[^\d]/g, ""));
  
  if (isNaN(breakpointWidth)) {
    return false;
  }
  
  return screenWidth >= breakpointWidth;
}

/**
 * Gets the best matching breakpoint based on screen width
 */
export async function getBreakpointFromScreenWidth(
  screenWidth: number
): Promise<BreakpointName> {
  const breakpoints = await getBreakpointConfig();
  
  // Sort breakpoints by width (ascending)
  const sortedBreakpoints = Object.entries(breakpoints)
    .map(([name, value]) => ({
      name: name as BreakpointName,
      width: parseInt(value.replace(/[^\d]/g, "")),
    }))
    .filter(({ width }) => !isNaN(width))
    .sort((a, b) => a.width - b.width);
  
  // Find the largest breakpoint that the screen width satisfies
  let matchingBreakpoint: BreakpointName = "mobile";
  
  for (const breakpoint of sortedBreakpoints) {
    if (screenWidth >= breakpoint.width) {
      matchingBreakpoint = breakpoint.name;
    } else {
      break;
    }
  }
  
  return matchingBreakpoint;
}

/**
 * Enhanced breakpoint detection that considers both device type and screen size
 */
export async function detectBreakpoint(
  deviceType: DeviceType,
  screenWidth?: number
): Promise<BreakpointName> {
  // If we have screen width, use it for more accurate detection
  if (screenWidth && screenWidth > 0) {
    return await getBreakpointFromScreenWidth(screenWidth);
  }
  
  // Otherwise, fall back to device type mapping
  return mapDeviceTypeToBreakpoint(deviceType);
}

/**
 * Validates if a breakpoint name is valid
 */
export function isValidBreakpoint(breakpoint: string): breakpoint is BreakpointName {
  return ["mobile", "tablet", "desktop", "default"].includes(breakpoint);
}

/**
 * Gets the CSS media query for a breakpoint
 */
export async function getMediaQueryForBreakpoint(
  breakpoint: BreakpointName
): Promise<string> {
  if (breakpoint === "default") {
    return "all";
  }
  
  const breakpoints = await getBreakpointConfig();
  const breakpointValue = breakpoints[breakpoint];
  
  if (!breakpointValue) {
    return "all";
  }
  
  const width = parseInt(breakpointValue.replace(/[^\d]/g, ""));
  
  if (isNaN(width)) {
    return "all";
  }
  
  return `(min-width: ${width}px)`;
}
