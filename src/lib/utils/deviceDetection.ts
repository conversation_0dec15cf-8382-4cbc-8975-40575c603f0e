import { headers } from "next/headers";

export type DeviceType = "mobile" | "tablet" | "desktop";

/**
 * Detects device type based on User-Agent string on the server side
 * @returns The detected device type
 */
export async function detectDeviceType(): Promise<DeviceType> {
  const headersList = await headers();
  const userAgent = headersList.get("user-agent") || "";

  // Mobile device patterns
  const mobilePatterns = [
    /Android.*Mobile/i,
    /iPhone/i,
    /iPod/i,
    /BlackBerry/i,
    /Windows Phone/i,
    /Opera Mini/i,
    /IEMobile/i,
    /Mobile.*Firefox/i,
  ];

  // Tablet device patterns
  const tabletPatterns = [
    /iPad/i,
    /Android(?!.*Mobile)/i,
    /Tablet/i,
    /PlayBook/i,
    /Kindle/i,
    /Silk/i,
    /KFOT/i,
    /KFTT/i,
    /KFJWI/i,
    /KFJWA/i,
    /KFSOWI/i,
    /KFTHWI/i,
    /KFTHWA/i,
    /KFAPWI/i,
    /KFAPWA/i,
  ];

  // Check for mobile first
  if (mobilePatterns.some((pattern) => pattern.test(userAgent))) {
    return "mobile";
  }

  // Check for tablet
  if (tabletPatterns.some((pattern) => pattern.test(userAgent))) {
    return "tablet";
  }

  // Default to desktop
  return "desktop";
}

/**
 * Gets screen size hints from User-Agent string
 * This is a fallback method and not as reliable as client-side detection
 */
export function getScreenSizeHints(userAgent: string): {
  width?: number;
  height?: number;
  devicePixelRatio?: number;
} {
  const hints: {
    width?: number;
    height?: number;
    devicePixelRatio?: number;
  } = {};

  // Try to extract screen size from User-Agent (limited support)
  const screenMatch = userAgent.match(/(\d+)x(\d+)/);
  if (screenMatch) {
    hints.width = parseInt(screenMatch[1]);
    hints.height = parseInt(screenMatch[2]);
  }

  // Common device resolutions based on User-Agent patterns
  if (/iPhone/i.test(userAgent)) {
    if (/iPhone.*OS 1[4-9]|iPhone.*OS [2-9]\d/i.test(userAgent)) {
      // Modern iPhones (iPhone 12+)
      hints.width = 390;
      hints.height = 844;
      hints.devicePixelRatio = 3;
    } else {
      // Older iPhones
      hints.width = 375;
      hints.height = 667;
      hints.devicePixelRatio = 2;
    }
  } else if (/iPad/i.test(userAgent)) {
    hints.width = 768;
    hints.height = 1024;
    hints.devicePixelRatio = 2;
  } else if (/Android.*Mobile/i.test(userAgent)) {
    // Android phones - vary widely, use common size
    hints.width = 360;
    hints.height = 640;
    hints.devicePixelRatio = 2;
  } else if (/Android/i.test(userAgent)) {
    // Android tablets
    hints.width = 800;
    hints.height = 1280;
    hints.devicePixelRatio = 1.5;
  }

  return hints;
}

/**
 * Enhanced device detection that considers both User-Agent and screen size hints
 */
export async function detectDeviceTypeEnhanced(): Promise<{
  deviceType: DeviceType;
  screenHints: {
    width?: number;
    height?: number;
    devicePixelRatio?: number;
  };
}> {
  const headersList = await headers();
  const userAgent = headersList.get("user-agent") || "";
  
  const deviceType = await detectDeviceType();
  const screenHints = getScreenSizeHints(userAgent);

  // Refine device type based on screen hints if available
  if (screenHints.width) {
    if (screenHints.width < 768) {
      return { deviceType: "mobile", screenHints };
    } else if (screenHints.width < 1024) {
      return { deviceType: "tablet", screenHints };
    } else {
      return { deviceType: "desktop", screenHints };
    }
  }

  return { deviceType, screenHints };
}
