{"website": {"id": "jstreet-dental", "name": "JStreet Dental Group", "domain": "jstreetdental.com", "globalSettings": {"theme": {"light": {"background": "rgb(249, 249, 249)", "foreground": "rgb(58, 58, 58)", "card": "rgb(255, 255, 255)", "cardForeground": "rgb(58, 58, 58)", "popover": "rgb(255, 255, 255)", "popoverForeground": "rgb(58, 58, 58)", "primary": "rgb(96, 96, 96)", "primaryForeground": "rgb(240, 240, 240)", "secondary": "rgb(222, 222, 222)", "secondaryForeground": "rgb(58, 58, 58)", "muted": "rgb(227, 227, 227)", "mutedForeground": "rgb(80, 80, 80)", "accent": "rgb(243, 234, 200)", "accentForeground": "rgb(93, 64, 55)", "destructive": "rgb(200, 122, 122)", "destructiveForeground": "rgb(255, 255, 255)", "border": "rgb(116, 114, 114)", "input": "rgb(255, 255, 255)", "ring": "rgb(160, 160, 160)", "chart1": "rgb(51, 51, 51)", "chart2": "rgb(85, 85, 85)", "chart3": "rgb(119, 119, 119)", "chart4": "rgb(153, 153, 153)", "chart5": "rgb(187, 187, 187)", "sidebar": "rgb(240, 240, 240)", "sidebarForeground": "rgb(58, 58, 58)", "sidebarPrimary": "rgb(96, 96, 96)", "sidebarPrimaryForeground": "rgb(240, 240, 240)", "sidebarAccent": "rgb(243, 234, 200)", "sidebarAccentForeground": "rgb(93, 64, 55)", "sidebarBorder": "rgb(192, 192, 192)", "sidebarRing": "rgb(160, 160, 160)"}, "dark": {"background": "rgb(43, 43, 43)", "foreground": "rgb(220, 220, 220)", "card": "rgb(51, 51, 51)", "cardForeground": "rgb(220, 220, 220)", "popover": "rgb(51, 51, 51)", "popoverForeground": "rgb(220, 220, 220)", "primary": "rgb(176, 176, 176)", "primaryForeground": "rgb(43, 43, 43)", "secondary": "rgb(90, 90, 90)", "secondaryForeground": "rgb(192, 192, 192)", "muted": "rgb(69, 69, 69)", "mutedForeground": "rgb(160, 160, 160)", "accent": "rgb(224, 224, 224)", "accentForeground": "rgb(51, 51, 51)", "destructive": "rgb(217, 175, 175)", "destructiveForeground": "rgb(43, 43, 43)", "border": "rgb(79, 79, 79)", "input": "rgb(51, 51, 51)", "ring": "rgb(192, 192, 192)", "chart1": "rgb(239, 239, 239)", "chart2": "rgb(208, 208, 208)", "chart3": "rgb(176, 176, 176)", "chart4": "rgb(144, 144, 144)", "chart5": "rgb(112, 112, 112)", "sidebar": "rgb(33, 33, 33)", "sidebarForeground": "rgb(220, 220, 220)", "sidebarPrimary": "rgb(176, 176, 176)", "sidebarPrimaryForeground": "rgb(33, 33, 33)", "sidebarAccent": "rgb(224, 224, 224)", "sidebarAccentForeground": "rgb(51, 51, 51)", "sidebarBorder": "rgb(79, 79, 79)", "sidebarRing": "rgb(192, 192, 192)"}, "fonts": {"sans": "Architects Daughter, sans-serif", "serif": "Georgia, serif", "mono": "\"Fira Code\", \"Courier New\", monospace"}, "radius": "0.625rem", "trackingNormal": "0.5px", "shadows": {"2xs": "1px 4px 5px 0px hsl(0 0% 0% / 0.01)", "xs": "1px 4px 5px 0px hsl(0 0% 0% / 0.01)", "sm": "1px 4px 5px 0px hsl(0 0% 0% / 0.03), 1px 1px 2px -1px hsl(0 0% 0% / 0.03)", "default": "1px 4px 5px 0px hsl(0 0% 0% / 0.03), 1px 1px 2px -1px hsl(0 0% 0% / 0.03)", "md": "1px 4px 5px 0px hsl(0 0% 0% / 0.03), 1px 2px 4px -1px hsl(0 0% 0% / 0.03)", "lg": "1px 4px 5px 0px hsl(0 0% 0% / 0.03), 1px 4px 6px -1px hsl(0 0% 0% / 0.03)", "xl": "1px 4px 5px 0px hsl(0 0% 0% / 0.03), 1px 8px 10px -1px hsl(0 0% 0% / 0.03)", "2xl": "1px 4px 5px 0px hsl(0 0% 0% / 0.07)", "3xl": "1px 4px 5px 0px hsl(0 0% 0% / 0.07)", "4xl": "1px 4px 5px 0px hsl(0 0% 0% / 0.07)", "inner": "inset 0 2px 4px 0 hsl(0 0% 0% / 0.05)"}, "spacingUnit": "0.25rem"}, "defaultLayoutId": "standardPageLayout", "breakpoints": {"mobile": "0px", "tablet": "768px", "desktop": "1024px"}}, "sections": [{"id": "navigation", "type": "navigation", "config": {"layout": "flex", "className": "flex justify-between items-center p-8 w-full border-b"}, "componentIds": ["siteLogo", "mainNavigation", "navActions"]}, {"id": "footer", "type": "footer", "config": {"layout": "flex", "className": "flex justify-between items-center bg-muted p-4 w-full"}, "componentIds": ["footerNavigation", "copyrightText"]}, {"id": "hero", "type": "hero", "config": {"layout": "flex", "className": "flex flex-col items-center justify-center p-16 bg-primary text-primary-foreground"}, "componentIds": ["<PERSON><PERSON><PERSON><PERSON>", "heroSubtitle", "heroAction"]}], "layouts": [{"id": "standardPageLayout", "description": "Default layout with header, main content area, and footer.", "type": "grid", "config": {"default": {"areas": ["header", "main", "footer"], "rows": ["auto", "1fr", "auto"], "columns": ["1fr"], "gap": "0"}, "tablet": {"areas": ["header", "main", "footer"], "rows": ["auto", "1fr", "auto"], "columns": ["1fr"], "gap": "0"}, "mobile": {"areas": ["header", "main", "footer"], "rows": ["auto", "1fr", "auto"], "columns": ["1fr"], "gap": "0"}}, "styles": {"backgroundColor": "var(--backgroundColor, #FFFFFF)", "maxWidth": "1400px", "margin": "0 auto", "height": "100vh", "display": "grid", "overflow": "hidden"}}, {"id": "landingPageLayout", "description": "Full-width layout for landing pages.", "type": "grid", "config": {"default": {"areas": ["hero", "content", "cta", "footer"], "rows": ["minmax(400px, auto)", "auto", "auto", "auto"], "columns": ["1fr"], "gap": "0"}}}, {"id": "modernLayout", "description": "Modern layout with header, main content (60%), and sidebar (40%)", "type": "grid", "config": {"default": {"areas": ["header header", "main sidebar", "footer footer"], "rows": ["auto", "1fr"], "columns": ["70%", "30%"], "gap": "0"}, "tablet": {"areas": ["header header", "main sidebar"], "rows": ["auto", "1fr"], "columns": ["50%", "50%"], "gap": "0"}, "mobile": {"areas": ["header", "main", "sidebar"], "rows": ["auto", "auto", "auto"], "columns": ["1fr"], "gap": "0"}}, "components": [{"componentId": "chatSidebar", "area": "sidebar"}], "styles": {"backgroundColor": "var(--backgroundColor, #FFFFFF)", "maxWidth": "1400px", "margin": "0 auto", "height": "100vh", "display": "grid", "overflow": "hidden"}}], "components": [{"id": "siteLogo", "type": "logo", "config": {"src": "https://placehold.co/150x40/FFFFFF/000000?text=JStreet+Dental", "altText": "JStreet Dental Group Logo", "link": "/", "width": 150, "height": 40}, "styles": {"maxWidth": "150px", "height": "auto", "backgroundColor": "var(--primary)"}}, {"id": "mainNavigation", "type": "navigationMenu", "config": {"orientation": "horizontal", "items": [{"id": "navHome", "icon": "home", "label": "Home", "link": "/home", "items": []}, {"id": "navAbout", "icon": "users", "label": "About", "link": "/about", "items": [{"id": "navTeam", "label": "Our Team", "link": "/about/team", "description": "Meet our experienced dental professionals"}, {"id": "navHistory", "label": "Our History", "link": "/about/history", "description": "Learn about our practice's journey"}]}, {"id": "navServices", "icon": "stethoscope", "label": "Services", "link": "/services", "items": [{"id": "navGeneral", "label": "General Dentistry", "link": "/services/general", "description": "Routine cleanings, fillings, and preventive care"}, {"id": "navCosmetic", "label": "Cosmetic Dentistry", "link": "/services/cosmetic", "description": "Teeth whitening, veneers, and smile makeovers"}, {"id": "nav<PERSON><PERSON><PERSON>", "label": "Orthodontics", "link": "/services/orthodontics", "description": "Braces, Invisalign, and other orthodontic treatments"}]}, {"id": "navContact", "icon": "phone", "label": "Contact", "link": "/contact", "items": []}]}}, {"id": "navActions", "type": "actions", "config": {"items": [{"id": "appointmentBtn", "label": "Book Appointment", "variant": "default"}]}}, {"id": "footerNavigation", "type": "navigationMenu", "config": {"orientation": "vertical", "items": [{"id": "footerHome", "label": "Home", "link": "/", "items": []}, {"id": "footerAbout", "label": "About", "link": "/about", "items": []}, {"id": "footerServices", "label": "Services", "link": "/services", "items": []}, {"id": "footerContact", "label": "Contact", "link": "/contact", "items": []}, {"id": "footerPrivacy", "label": "Privacy Policy", "link": "/privacy", "items": []}]}}, {"id": "copyrightText", "type": "paragraph", "config": {"content": "© 2024 JStreet Dental Group. All rights reserved.", "className": "text-center text-sm", "styles": {"fontSize": "calc(3 * var(--spacingUnit, 0.25rem))", "padding": "calc(4 * var(--spacingUnit, 0.25rem)) 0"}}}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "type": "header", "config": {"content": "Your Brightest Smile Starts Here", "type": "h1", "className": "text-4xl font-bold text-center"}}, {"id": "heroSubtitle", "type": "paragraph", "config": {"content": "Comprehensive dental care in a friendly environment", "className": "text-xl mt-4 text-center"}}, {"id": "heroAction", "type": "actions", "config": {"items": [{"id": "scheduleBtn", "label": "Schedule Appointment", "variant": "secondary", "link": "/contact"}]}}, {"id": "chatSidebar", "type": "chat", "config": {"title": "Chat Support", "subtitle": "We typically reply within minutes", "placeholder": "Type a message...", "sendButtonText": "Send", "initialMessages": [{"sender": "support", "content": "Hello! How can I help you today?", "timestamp": "2023-06-15T10:30:00Z"}], "automatedResponses": [{"keywords": ["appointment", "schedule", "book"], "response": "I'd be happy to help you schedule an appointment. Please call us at (************* or use our online booking form on the contact page."}, {"keywords": ["hours", "open"], "response": "Our office hours are Monday-Friday 8am-5pm and Saturday 9am-2pm. We're closed on Sundays."}, {"keywords": ["insurance", "payment"], "response": "We accept most major insurance plans and offer various payment options. Would you like more specific information?"}, {"keywords": ["emergency"], "response": "If you're experiencing a dental emergency, please call us immediately at (*************. For after-hours emergencies, our answering service will connect you with the on-call dentist."}, {"keywords": ["service", "treatment"], "response": "We offer a wide range of dental services including preventive care, restorative treatments, cosmetic procedures, and more. Is there a specific service you're interested in?"}], "defaultResponse": "Thank you for your message. How else can I assist you with your dental care needs?"}, "styles": {"height": "100%", "backgroundColor": "var(--card)", "borderLeft": "1px solid var(--border)"}}], "pages": [{"id": "home", "path": "/home", "title": "Welcome to JStreet Dental Group", "layoutId": "standardPageLayout", "elements": [{"componentId": "siteLogo", "area": "header"}, {"componentId": "mainNavigation", "area": "header"}, {"type": "container", "area": "main", "layout": {"type": "flex", "config": {"direction": "column", "gap": "calc(6 * var(--spacingUnit, 0.25rem))", "mobile": {"direction": "column", "gap": "calc(4 * var(--spacingUnit, 0.25rem))"}}}, "elements": [{"type": "container", "config": {"className": "relative w-full h-[500px] overflow-hidden rounded-lg"}, "elements": [{"type": "image", "config": {"src": "https://placehold.co/1200x500/DDDDDD/444444?text=Dental+Care", "alt": "Dental care hero image", "width": 1200, "height": 500, "className": "w-full h-full object-cover"}}, {"type": "container", "config": {"className": "absolute inset-0 bg-gradient-to-r from-primary/80 to-transparent flex items-center"}, "elements": [{"type": "container", "config": {"className": "p-8 md:p-12 max-w-md"}, "layout": {"type": "flex", "config": {"direction": "column", "gap": "1.5rem"}}, "elements": [{"type": "header", "config": {"content": "Your Brightest Smile Starts Here", "type": "h1", "className": "text-3xl md:text-4xl font-bold text-white"}}, {"type": "paragraph", "config": {"content": "Comprehensive dental care in a friendly environment", "className": "text-lg md:text-xl text-white/90"}}, {"type": "button", "config": {"label": "Schedule Appointment", "variant": "secondary", "size": "lg", "className": "mt-2", "link": "/contact"}}]}]}]}, {"type": "container", "layout": {"type": "grid", "config": {"columns": "repeat(3, 1fr)", "gap": "2rem", "tablet": {"columns": "repeat(2, 1fr)"}, "mobile": {"columns": "1fr"}}}, "elements": [{"type": "card", "config": {"className": " text-center justify-between", "elements": [{"type": "icon", "config": {"name": "calendar", "size": 48, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "<PERSON>heduling", "type": "h3", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Book appointments online or by phone at your convenience.", "className": "text-muted-foreground mb-4"}}], "footer": [{"type": "button", "config": {"label": "Book Now", "variant": "outline", "size": "sm", "className": "mx-auto", "link": "/contact"}}]}}, {"type": "card", "config": {"className": " text-center justify-between", "elements": [{"type": "icon", "config": {"name": "heart-pulse", "size": 48, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Comprehensive Services", "type": "h3", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "From cleanings to orthodontics, we've got you covered.", "className": "text-muted-foreground mb-4"}}], "footer": [{"type": "button", "config": {"label": "Learn More", "variant": "outline", "size": "sm", "className": "mx-auto", "link": "/services"}}]}}, {"type": "card", "config": {"className": " text-center justify-between", "elements": [{"type": "icon", "config": {"name": "heart", "size": 48, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Patient-Centered Care", "type": "h3", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Your comfort and satisfaction are our top priorities.", "className": "text-muted-foreground mb-4"}}], "footer": [{"type": "button", "config": {"label": "Meet Our Team", "variant": "outline", "size": "sm", "className": "mx-auto", "link": "/about/team"}}]}}]}, {"type": "card", "config": {"className": "bg-accent text-accent-foreground p-6 rounded-lg shadow-md border border-accent justify-between", "elements": [{"type": "header", "config": {"content": "New Patient Special", "type": "h3", "className": "text-xl font-bold mb-2"}}, {"type": "paragraph", "config": {"content": "Get a comprehensive exam and cleaning for just $99!", "className": "mb-4"}}], "footer": [{"type": "button", "config": {"label": "<PERSON><PERSON><PERSON>", "link": "/contact", "variant": "secondary", "size": "default", "className": "w-full"}}]}}, {"type": "card", "config": {"className": "bg-primary/10 p-6 rounded-lg shadow-sm border justify-between", "elements": [{"type": "header", "config": {"content": "Schedule Your Visit", "type": "h2", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Ready to experience our exceptional dental care? Book your appointment today.", "className": "mb-4 text-sm"}}], "footer": [{"type": "button", "config": {"label": "Book Appointment", "variant": "default", "size": "default", "className": "w-full", "link": "/contact"}}]}}, {"type": "form", "config": {"id": "appointmentForm", "title": "Request Appointment", "submitEndpoint": "/api/mock/submit-form", "method": "POST", "className": "bg-card p-6 rounded-lg shadow-md border", "elements": [{"type": "header", "config": {"content": "Request Appointment", "type": "h3", "className": "text-xl font-bold mb-4"}}, {"type": "formField", "config": {"id": "name", "label": "Full Name", "type": "text", "placeholder": "Enter your full name", "required": true, "className": "mb-4"}}, {"type": "formField", "config": {"id": "email", "label": "Email Address", "type": "email", "placeholder": "<EMAIL>", "required": true, "className": "mb-4"}}, {"type": "formField", "config": {"id": "phone", "label": "Phone Number", "type": "tel", "placeholder": "(*************", "required": true, "className": "mb-4"}}, {"type": "formField", "config": {"id": "preferredDate", "label": "Preferred Date", "type": "date", "required": true, "className": "mb-4"}}, {"type": "formField", "config": {"id": "message", "label": "Additional Information", "type": "textarea", "placeholder": "Tell us about your dental needs or preferred time of day", "required": false, "className": "mb-6"}}, {"type": "button", "config": {"label": "Submit Request", "type": "submit", "variant": "default", "size": "default", "className": "w-full"}}], "successMessage": "Thank you! We'll contact you shortly."}}]}]}, {"id": "about", "path": "/about", "title": "About JStreet Dental Group", "layoutId": "standardPageLayout", "elements": [{"componentId": "siteLogo", "area": "header"}, {"componentId": "mainNavigation", "area": "header"}, {"type": "container", "area": "main", "layout": {"type": "flex", "config": {"direction": "column", "gap": "calc(6 * var(--spacingUnit, 0.25rem))"}}, "elements": [{"type": "container", "config": {"className": "bg-primary/10 py-12 px-6 rounded-lg"}, "layout": {"type": "flex", "config": {"direction": "column", "alignItems": "center", "gap": "1.5rem"}}, "elements": [{"type": "header", "config": {"content": "About JStreet Dental Group", "type": "h1", "className": "text-3xl font-bold text-center"}}, {"type": "paragraph", "config": {"content": "Providing exceptional dental care since 2005", "className": "text-xl text-center max-w-2xl"}}]}, {"type": "container", "layout": {"type": "grid", "config": {"columns": "1fr 1fr", "gap": "3rem", "mobile": {"columns": "1fr"}}}, "elements": [{"type": "image", "config": {"src": "https://placehold.co/600x400/DDDDDD/444444?text=Dental+Office", "alt": "Our modern dental office", "width": 600, "height": 400, "className": "rounded-lg shadow-md w-full h-auto"}}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "1.5rem", "justifyContent": "center"}}, "elements": [{"type": "header", "config": {"content": "Our Practice", "type": "h2", "className": "text-2xl font-bold"}}, {"type": "paragraph", "config": {"content": "Founded in 2005, JStreet Dental Group has been providing exceptional dental care to our community for nearly two decades. Our state-of-the-art facility is designed with your comfort in mind.", "className": "text-muted-foreground"}}, {"type": "paragraph", "config": {"content": "We believe in a patient-centered approach to dentistry, focusing on preventive care and education to help you maintain optimal oral health for life.", "className": "text-muted-foreground"}}, {"type": "button", "config": {"label": "Meet Our Team", "variant": "outline", "size": "default", "className": "mt-2", "link": "/about/team"}}]}]}, {"type": "container", "config": {"className": "bg-card p-8 rounded-lg shadow-sm border"}, "elements": [{"type": "header", "config": {"content": "Our Mission", "type": "h2", "className": "text-2xl font-bold mb-4 text-center"}}, {"type": "paragraph", "config": {"content": "At JStreet Dental Group, our mission is to provide comprehensive, high-quality dental care in a comfortable and welcoming environment. We are committed to helping our patients achieve and maintain optimal oral health through personalized treatment plans and education.", "className": "text-center max-w-3xl mx-auto"}}]}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "2rem"}}, "elements": [{"type": "header", "config": {"content": "Meet Our Team", "type": "h2", "className": "text-2xl font-bold"}}, {"type": "container", "layout": {"type": "grid", "config": {"columns": "repeat(3, 1fr)", "gap": "2rem", "tablet": {"columns": "repeat(2, 1fr)"}, "mobile": {"columns": "1fr"}}}, "elements": [{"type": "card", "config": {"className": " overflow-hidden", "elements": [{"type": "image", "config": {"src": "https://placehold.co/300x300/EEEEEE/333333?text=Dr.+<PERSON>", "alt": "Dr. <PERSON>", "width": 300, "height": 300, "className": "w-full h-64 object-cover object-center rounded-md mb-4"}}, {"type": "header", "config": {"content": "Dr. <PERSON>", "type": "h3", "className": "text-xl font-semibold mb-1"}}, {"type": "paragraph", "config": {"content": "Lead Dentist", "className": "text-primary font-medium mb-2"}}, {"type": "paragraph", "config": {"content": "Dr<PERSON> has over 15 years of experience in general and cosmetic dentistry.", "className": "text-muted-foreground text-sm"}}]}}, {"type": "card", "config": {"className": " overflow-hidden", "elements": [{"type": "image", "config": {"src": "https://placehold.co/300x300/EEEEEE/333333?text=<PERSON><PERSON>+<PERSON>", "alt": "Dr. <PERSON>", "width": 300, "height": 300, "className": "w-full h-64 object-cover object-center rounded-md mb-4"}}, {"type": "header", "config": {"content": "Dr. <PERSON>", "type": "h3", "className": "text-xl font-semibold mb-1"}}, {"type": "paragraph", "config": {"content": "Orthodontist", "className": "text-primary font-medium mb-2"}}, {"type": "paragraph", "config": {"content": "Dr. <PERSON> specializes in orthodontics and is certified in Invisalign treatment.", "className": "text-muted-foreground text-sm"}}]}}, {"type": "card", "config": {"className": "overflow-hidden", "elements": [{"type": "image", "config": {"src": "https://placehold.co/300x300/EEEEEE/333333?text=Lisa+R", "alt": "<PERSON>", "width": 300, "height": 300, "className": "w-full h-64 object-cover object-center rounded-md mb-4"}}, {"type": "header", "config": {"content": "<PERSON>", "type": "h3", "className": "text-xl font-semibold mb-1"}}, {"type": "paragraph", "config": {"content": "Dental Hygienist", "className": "text-primary font-medium mb-2"}}, {"type": "paragraph", "config": {"content": "<PERSON> ensures our patients receive thorough and gentle cleanings.", "className": "text-muted-foreground text-sm"}}]}}]}]}, {"type": "container", "config": {"className": "bg-accent/30 p-8 rounded-lg"}, "layout": {"type": "flex", "config": {"direction": "column", "gap": "1.5rem", "alignItems": "center"}}, "elements": [{"type": "header", "config": {"content": "Our Approach to Dental Care", "type": "h2", "className": "text-2xl font-bold text-center"}}, {"type": "container", "layout": {"type": "grid", "config": {"columns": "repeat(3, 1fr)", "gap": "2rem", "tablet": {"columns": "repeat(2, 1fr)"}, "mobile": {"columns": "1fr"}}}, "elements": [{"type": "container", "config": {"className": "bg-background p-6 rounded-lg shadow-sm text-center"}, "elements": [{"type": "icon", "config": {"name": "heart", "size": 36, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Patient-Centered", "type": "h3", "className": "text-lg font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "We prioritize your comfort and individual needs in all treatments.", "className": "text-sm text-muted-foreground"}}]}, {"type": "container", "config": {"className": "bg-background p-6 rounded-lg shadow-sm text-center"}, "elements": [{"type": "icon", "config": {"name": "shield", "size": 36, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Preventive Focus", "type": "h3", "className": "text-lg font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "We emphasize prevention to help you maintain optimal oral health.", "className": "text-sm text-muted-foreground"}}]}, {"type": "container", "config": {"className": "bg-background p-6 rounded-lg shadow-sm text-center"}, "elements": [{"type": "icon", "config": {"name": "graduationCap", "size": 36, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Continued Education", "type": "h3", "className": "text-lg font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Our team stays current with the latest dental techniques and technologies.", "className": "text-sm text-muted-foreground"}}]}]}]}]}, {"type": "container", "area": "sidebar", "layout": {"type": "flex", "config": {"direction": "column", "gap": "2rem"}}, "elements": [{"type": "card", "config": {"className": "", "elements": [{"type": "header", "config": {"content": "What Our Patients Say", "type": "h2", "className": "text-xl font-semibold mb-4"}}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "1.5rem"}}, "elements": [{"type": "container", "config": {"className": "bg-muted/50 p-4 rounded-lg"}, "elements": [{"type": "paragraph", "config": {"content": "The best dental experience I've ever had! Dr<PERSON> and his team are professional, gentle, and truly care about their patients.", "className": "italic text-sm mb-2"}}, {"type": "container", "config": {"className": "flex items-center justify-between"}, "elements": [{"type": "paragraph", "config": {"content": "<PERSON>", "className": "font-medium text-sm"}}, {"type": "container", "config": {"className": "flex"}, "elements": [{"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}]}]}]}, {"type": "container", "config": {"className": "bg-muted/50 p-4 rounded-lg"}, "elements": [{"type": "paragraph", "config": {"content": "Professional staff and beautiful results. I couldn't be happier with my smile makeover!", "className": "italic text-sm mb-2"}}, {"type": "container", "config": {"className": "flex items-center justify-between"}, "elements": [{"type": "paragraph", "config": {"content": "Maria S.", "className": "font-medium text-sm"}}, {"type": "container", "config": {"className": "flex"}, "elements": [{"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}]}]}]}]}]}}, {"type": "card", "config": {"className": "bg-primary/10 p-6 rounded-lg shadow-sm border", "elements": [{"type": "header", "config": {"content": "Schedule Your Visit", "type": "h2", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Ready to experience our exceptional dental care? Book your appointment today.", "className": "mb-4 text-sm"}}, {"type": "button", "config": {"label": "Book Appointment", "variant": "default", "size": "default", "className": "w-full", "link": "/contact"}}]}}]}]}, {"id": "services", "path": "/services", "title": "Our Dental Services", "layoutId": "standardPageLayout", "elements": [{"componentId": "siteLogo", "area": "header"}, {"componentId": "mainNavigation", "area": "header"}, {"type": "container", "area": "main", "layout": {"type": "flex", "config": {"direction": "column", "gap": "calc(5 * var(--spacingUnit, 0.25rem))"}}, "elements": [{"type": "container", "config": {"className": "bg-primary/10 py-10 px-6 rounded-lg"}, "layout": {"type": "flex", "config": {"direction": "column", "alignItems": "center", "gap": "1rem"}}, "elements": [{"type": "header", "config": {"content": "Our Dental Services", "type": "h1", "className": "text-3xl font-bold text-center"}}, {"type": "paragraph", "config": {"content": "Comprehensive care for all your dental needs", "className": "text-xl text-center"}}]}, {"type": "container", "layout": {"type": "grid", "config": {"columns": "repeat(3, 1fr)", "gap": "2rem", "tablet": {"columns": "repeat(2, 1fr)"}, "mobile": {"columns": "1fr"}}}, "elements": [{"type": "card", "config": {"className": " text-center justify-between", "elements": [{"type": "icon", "config": {"name": "stethoscope", "size": 48, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "General Dentistry", "type": "h3", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Regular checkups, cleanings, fillings, and preventive care to maintain your oral health.", "className": "text-muted-foreground mb-4"}}], "footer": [{"type": "link", "config": {"href": "/services/general", "content": "Learn more →", "className": "mx-auto text-primary hover:underline font-medium"}}]}}, {"type": "card", "config": {"className": " text-center justify-between", "elements": [{"type": "icon", "config": {"name": "smile", "size": 48, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Cosmetic Dentistry", "type": "h3", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Teeth whitening, veneers, bonding, and other procedures to enhance your smile.", "className": "text-muted-foreground mb-4"}}], "footer": [{"type": "link", "config": {"href": "/services/cosmetic", "content": "Learn more →", "className": "mx-auto text-primary hover:underline font-medium"}}]}}, {"type": "card", "config": {"className": " text-center justify-between", "elements": [{"type": "icon", "config": {"name": "align-left", "size": 48, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Orthodontics", "type": "h3", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Traditional braces, clear aligners, and other treatments to straighten teeth.", "className": "text-muted-foreground mb-4"}}], "footer": [{"type": "link", "config": {"href": "/services/orthodontics", "content": "Learn more →", "className": "mx-auto text-primary hover:underline font-medium"}}]}}, {"type": "card", "config": {"className": " text-center justify-between", "elements": [{"type": "icon", "config": {"name": "baby", "size": 48, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Pediatric Dentistry", "type": "h3", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Gentle dental care for children in a kid-friendly environment.", "className": "text-muted-foreground mb-4"}}], "footer": [{"type": "link", "config": {"href": "/services/pediatric", "content": "Learn more →", "className": "mx-auto text-primary hover:underline font-medium"}}]}}, {"type": "card", "config": {"className": " text-center justify-between", "elements": [{"type": "icon", "config": {"name": "scissors", "size": 48, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Oral Surgery", "type": "h3", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Extractions, implants, and other surgical procedures.", "className": "text-muted-foreground mb-4"}}], "footer": [{"type": "link", "config": {"href": "/services/surgery", "content": "Learn more →", "className": "mx-auto text-primary hover:underline font-medium"}}]}}, {"type": "card", "config": {"className": " text-center justify-between", "elements": [{"type": "icon", "config": {"name": "circle-alert", "size": 48, "className": "text-primary mx-auto mb-4"}}, {"type": "header", "config": {"content": "Emergency Care", "type": "h3", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Same-day appointments for dental emergencies.", "className": "text-muted-foreground mb-4"}}], "footer": [{"type": "link", "config": {"href": "/services/emergency", "content": "Learn more →", "className": "mx-auto text-primary hover:underline font-medium"}}]}}]}, {"type": "container", "config": {"className": "bg-accent/30 p-8 rounded-lg mt-6"}, "layout": {"type": "flex", "config": {"direction": "column", "gap": "1.5rem", "alignItems": "center"}}, "elements": [{"type": "header", "config": {"content": "Not Sure What You Need?", "type": "h2", "className": "text-2xl font-bold text-center"}}, {"type": "paragraph", "config": {"content": "Schedule a consultation with our team to discuss your dental needs and create a personalized treatment plan.", "className": "text-center max-w-2xl"}}, {"type": "button", "config": {"label": "Schedule Consultation", "variant": "default", "size": "lg", "className": "mt-2", "link": "/contact"}}]}]}, {"type": "container", "area": "sidebar", "layout": {"type": "flex", "config": {"direction": "column", "gap": "2rem"}}, "elements": [{"type": "card", "config": {"className": "", "elements": [{"type": "header", "config": {"content": "Insurance Information", "type": "h2", "className": "text-xl font-semibold mb-4"}}, {"type": "paragraph", "config": {"content": "We accept most major insurance plans. Contact our office to verify your coverage.", "className": "mb-4 text-sm"}}, {"type": "container", "layout": {"type": "grid", "config": {"columns": "repeat(3, 1fr)", "gap": "1rem"}}, "elements": [{"type": "image", "config": {"src": "https://placehold.co/80x40/EEEEEE/555555?text=Insurance+1", "alt": "Insurance Provider 1", "width": 80, "height": 40, "className": "w-full h-auto"}}, {"type": "image", "config": {"src": "https://placehold.co/80x40/EEEEEE/555555?text=Insurance+2", "alt": "Insurance Provider 2", "width": 80, "height": 40, "className": "w-full h-auto"}}, {"type": "image", "config": {"src": "https://placehold.co/80x40/EEEEEE/555555?text=Insurance+3", "alt": "Insurance Provider 3", "width": 80, "height": 40, "className": "w-full h-auto"}}]}]}}, {"type": "card", "config": {"className": "bg-primary/10 p-6 rounded-lg shadow-sm border", "elements": [{"type": "header", "config": {"content": "Schedule Your Checkup", "type": "h2", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Regular dental checkups are essential for maintaining good oral health.", "className": "mb-4 text-sm"}}, {"type": "button", "config": {"label": "Book Appointment", "variant": "default", "size": "default", "className": "w-full", "link": "/contact"}}]}}]}]}, {"id": "contact", "path": "/contact", "title": "Contact JStreet Dental Group", "layoutId": "standardPageLayout", "elements": [{"componentId": "siteLogo", "area": "header"}, {"componentId": "mainNavigation", "area": "header"}, {"type": "container", "area": "main", "layout": {"type": "flex", "config": {"direction": "column", "gap": "calc(4 * var(--spacingUnit, 0.25rem))"}}, "elements": [{"type": "header", "config": {"content": "Contact Us", "type": "h1", "className": "text-3xl font-bold"}}, {"type": "paragraph", "config": {"content": "We'd love to hear from you. Reach out with any questions or to schedule an appointment.", "className": "text-lg text-muted-foreground mb-6"}}, {"type": "container", "layout": {"type": "grid", "config": {"columns": "1fr 1fr", "gap": "2rem", "mobile": {"columns": "1fr"}}}, "elements": [{"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "1.5rem"}}, "elements": [{"type": "card", "config": {"className": "p-6 bg-card shadow-sm border", "elements": [{"type": "header", "config": {"content": "Office Information", "type": "h2", "className": "text-xl font-semibold mb-4"}}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "1rem"}}, "elements": [{"type": "container", "config": {"className": "flex items-start gap-3"}, "elements": [{"type": "icon", "config": {"name": "mapPin", "className": "mt-1 text-primary", "size": 20}}, {"type": "container", "config": {"className": "ml-3"}, "elements": [{"type": "paragraph", "config": {"content": "Address", "className": "font-medium text-sm text-muted-foreground"}}, {"type": "paragraph", "config": {"content": "123 J Street\nAnytown, USA 12345", "className": "whitespace-pre-line"}}]}]}, {"type": "container", "config": {"className": "flex items-start gap-3"}, "elements": [{"type": "icon", "config": {"name": "phone", "className": "mt-1 text-primary", "size": 20}}, {"type": "container", "config": {"className": "ml-3"}, "elements": [{"type": "paragraph", "config": {"content": "Phone", "className": "font-medium text-sm text-muted-foreground"}}, {"type": "link", "config": {"content": "(*************", "href": "tel:+15551234567", "className": "hover:underline"}}]}]}, {"type": "container", "config": {"className": "flex items-start gap-3"}, "elements": [{"type": "icon", "config": {"name": "mail", "className": "mt-1 text-primary", "size": 20}}, {"type": "container", "config": {"className": "ml-3"}, "elements": [{"type": "paragraph", "config": {"content": "Email", "className": "font-medium text-sm text-muted-foreground"}}, {"type": "link", "config": {"content": "<EMAIL>", "href": "mailto:<EMAIL>", "className": "hover:underline"}}]}]}]}]}}, {"type": "card", "config": {"className": "p-6 bg-card shadow-sm border", "elements": [{"type": "header", "config": {"content": "Office Hours", "type": "h2", "className": "text-xl font-semibold mb-4"}}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "0.5rem"}}, "elements": [{"type": "container", "config": {"className": "flex justify-between py-2 border-b border-border"}, "elements": [{"type": "paragraph", "config": {"content": "Monday-Thursday", "className": "font-medium"}}, {"type": "paragraph", "config": {"content": "8:00 AM - 5:00 PM", "className": ""}}]}, {"type": "container", "config": {"className": "flex justify-between py-2 border-b border-border"}, "elements": [{"type": "paragraph", "config": {"content": "Friday", "className": "font-medium"}}, {"type": "paragraph", "config": {"content": "8:00 AM - 2:00 PM", "className": ""}}]}, {"type": "container", "config": {"className": "flex justify-between py-2 border-b border-border last:border-0"}, "elements": [{"type": "paragraph", "config": {"content": "Saturday-Sunday", "className": "font-medium"}}, {"type": "paragraph", "config": {"content": "Closed", "className": "text-destructive"}}]}]}]}}, {"type": "iframe", "config": {"src": "https://www.google.com/maps/embed/v1/place?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8&q=123+J+Street,Anytown,USA&zoom=15", "title": "Office Location", "width": "100%", "height": 250, "className": "rounded-lg overflow-hidden shadow-sm border"}}]}, {"type": "form", "config": {"id": "contactForm", "title": "Send Us a Message", "submitEndpoint": "/api/mock/submit-form", "method": "POST", "className": "bg-card p-6 rounded-lg shadow-md border", "elements": [{"type": "header", "config": {"content": "Send Us a Message", "type": "h2", "className": "text-xl font-semibold mb-4"}}, {"type": "formField", "config": {"id": "name", "label": "Full Name", "type": "text", "placeholder": "Enter your full name", "required": true, "className": "mb-4"}}, {"type": "formField", "config": {"id": "email", "label": "Email Address", "type": "email", "placeholder": "<EMAIL>", "required": true, "className": "mb-4"}}, {"type": "formField", "config": {"id": "phone", "label": "Phone Number", "type": "tel", "placeholder": "(*************", "required": false, "className": "mb-4"}}, {"type": "formField", "config": {"id": "subject", "label": "Subject", "type": "select", "required": true, "className": "mb-4", "options": [{"value": "appointment", "label": "Schedule Appointment"}, {"value": "question", "label": "General Question"}, {"value": "feedback", "label": "<PERSON><PERSON><PERSON>"}, {"value": "billing", "label": "Billing Inquiry"}, {"value": "other", "label": "Other"}]}}, {"type": "formField", "config": {"id": "message", "label": "Message", "type": "textarea", "placeholder": "How can we help you?", "required": true, "className": "mb-6"}}, {"type": "button", "config": {"label": "Send Message", "type": "submit", "variant": "default", "size": "default", "className": "w-full"}}], "successMessage": "Thank you for your message. We'll respond shortly."}}]}]}]}, {"id": "services-general", "path": "/services/general", "title": "General Dentistry Services", "layoutId": "standardPageLayout", "elements": [{"componentId": "siteLogo", "area": "header"}, {"componentId": "mainNavigation", "area": "header"}, {"type": "container", "area": "main", "layout": {"type": "flex", "config": {"direction": "column", "gap": "calc(5 * var(--spacingUnit, 0.25rem))"}}, "elements": [{"type": "container", "config": {"className": "bg-primary/10 py-8 px-6 rounded-lg"}, "layout": {"type": "flex", "config": {"direction": "column", "alignItems": "center", "gap": "1rem"}}, "elements": [{"type": "header", "config": {"content": "General Dentistry", "type": "h1", "className": "text-3xl font-bold text-center"}}, {"type": "paragraph", "config": {"content": "Our comprehensive general dentistry services help maintain your oral health and prevent future problems.", "className": "text-lg text-center max-w-2xl"}}]}, {"type": "container", "layout": {"type": "grid", "config": {"columns": "2fr 1fr", "gap": "2rem", "mobile": {"columns": "1fr"}}}, "elements": [{"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "2rem"}}, "elements": [{"type": "container", "config": {"className": "bg-card p-6 rounded-lg shadow-sm border"}, "elements": [{"type": "header", "config": {"content": "Comprehensive Dental Exams", "type": "h2", "className": "text-2xl font-semibold mb-3"}}, {"type": "paragraph", "config": {"content": "Our thorough dental examinations include:", "className": "mb-3"}}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "0.5rem"}}, "elements": [{"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Visual examination of teeth, gums, and oral tissues", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Digital X-rays to detect issues not visible to the naked eye", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Oral cancer screening", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Evaluation of bite and jaw alignment", "className": ""}}]}]}, {"type": "paragraph", "config": {"content": "We recommend comprehensive exams every six months to catch potential issues early.", "className": "mt-3"}}]}, {"type": "container", "config": {"className": "bg-card p-6 rounded-lg shadow-sm border"}, "elements": [{"type": "header", "config": {"content": "Professional Cleanings", "type": "h2", "className": "text-2xl font-semibold mb-3"}}, {"type": "paragraph", "config": {"content": "Regular professional cleanings are essential for maintaining good oral health. Our hygienists will:", "className": "mb-3"}}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "0.5rem"}}, "elements": [{"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Remove plaque and tartar buildup", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Polish teeth to remove surface stains", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Provide personalized oral hygiene instructions", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Apply fluoride treatment if needed", "className": ""}}]}]}]}, {"type": "container", "config": {"className": "bg-card p-6 rounded-lg shadow-sm border"}, "elements": [{"type": "header", "config": {"content": "Fillings & Restorations", "type": "h2", "className": "text-2xl font-semibold mb-3"}}, {"type": "paragraph", "config": {"content": "We offer tooth-colored composite fillings that blend seamlessly with your natural teeth. Our restorative services include:", "className": "mb-3"}}, {"type": "container", "layout": {"type": "grid", "config": {"columns": "1fr 1fr", "gap": "1rem", "mobile": {"columns": "1fr"}}}, "elements": [{"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Composite fillings", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Crowns and bridges", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Root canal therapy", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Implants", "className": ""}}]}]}]}, {"type": "container", "config": {"className": "bg-card p-6 rounded-lg shadow-sm border"}, "elements": [{"type": "header", "config": {"content": "Oral Cancer Screening", "type": "h2", "className": "text-2xl font-semibold mb-3"}}, {"type": "paragraph", "config": {"content": "We perform oral cancer screenings as part of our comprehensive exams. Early detection is key to successful treatment.", "className": "mb-3"}}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "0.5rem"}}, "elements": [{"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Dentures", "className": ""}}]}]}, {"type": "image", "config": {"src": "https://placehold.co/600x300/EEEEEE/444444?text=Dental+Restoration", "alt": "Dental restoration example", "width": 600, "height": 300, "className": "w-full h-auto rounded-md mt-4"}}]}, {"type": "container", "config": {"className": "bg-card p-6 rounded-lg shadow-sm border"}, "elements": [{"type": "header", "config": {"content": "Preventive Care", "type": "h2", "className": "text-2xl font-semibold mb-3"}}, {"type": "paragraph", "config": {"content": "We believe prevention is the best approach to dental care. Our preventive services include:", "className": "mb-3"}}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "0.5rem"}}, "elements": [{"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Dental sealants to protect teeth from decay", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Fluoride treatments to strengthen enamel", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Custom mouthguards for sports and nighttime grinding", "className": ""}}]}, {"type": "container", "config": {"className": "flex items-start"}, "elements": [{"type": "icon", "config": {"name": "check", "size": 20, "className": "text-primary mr-2 mt-1 flex-shrink-0"}}, {"type": "paragraph", "config": {"content": "Oral hygiene education and personalized care plans", "className": ""}}]}]}]}]}, {"type": "container", "layout": {"type": "flex", "config": {"direction": "column", "gap": "2rem"}}, "elements": [{"type": "card", "config": {"className": "bg-primary/10 p-6 rounded-lg shadow-sm border sticky top-4 justify-between", "elements": [{"type": "header", "config": {"content": "Schedule Your Checkup", "type": "h2", "className": "text-xl font-semibold mb-2"}}, {"type": "paragraph", "config": {"content": "Regular dental checkups are essential for maintaining good oral health.", "className": "mb-4 text-sm"}}], "footer": [{"type": "button", "config": {"label": "Book Appointment", "variant": "default", "size": "default", "className": "w-full", "link": "/contact"}}]}}, {"type": "card", "config": {"elements": [{"type": "header", "config": {"content": "Patient Testimonial", "type": "h3", "className": "text-lg font-semibold mb-3"}}, {"type": "container", "config": {"className": "bg-muted/50 p-4 rounded-lg"}, "elements": [{"type": "paragraph", "config": {"content": "I've been coming to JStreet Dental for my regular checkups for years. The staff is always friendly and professional, and Dr<PERSON> takes the time to explain everything clearly.", "className": "italic text-sm mb-2"}}, {"type": "container", "config": {"className": "flex items-center justify-between"}, "elements": [{"type": "paragraph", "config": {"content": "<PERSON>", "className": "font-medium text-sm"}}, {"type": "container", "config": {"className": "flex"}, "elements": [{"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}, {"type": "icon", "config": {"name": "star", "size": 16, "className": "text-yellow-500"}}]}]}]}]}}]}]}]}]}]}}